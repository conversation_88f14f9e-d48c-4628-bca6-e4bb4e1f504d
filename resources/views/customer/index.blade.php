<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学员列表</title>
    {{-- 为了快速实现样式，这里使用内联CSS。在实际项目中，建议将CSS移至独立的 .css 文件 --}}
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            background-color: #f4f6f8;
            color: #333;
            font-size: 14px;
            margin: 20px;
        }

        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .tabs {
            border-bottom: 2px solid #eef0f3;
            margin-bottom: 20px;
        }

        .tabs button {
            border: none;
            background: none;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: -2px;
        }

        .tabs button.active {
            color: #4a90e2;
            border-bottom: 2px solid #4a90e2;
            font-weight: 600;
        }

        .filter-bar {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-bar input, .filter-bar select {
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 8px 12px;
            height: 36px;
        }

        .filter-bar .search-input {
            width: 200px;
        }

        .filter-bar .btn {
            border: none;
            border-radius: 4px;
            padding: 8px 20px;
            cursor: pointer;
            font-size: 14px;
            height: 36px;
        }

        .filter-bar .btn-primary {
            background-color: #4a90e2;
            color: white;
        }

        .filter-bar .btn-primary:hover {
            background-color: #357abd;
        }

        .filter-bar .spacer {
            flex-grow: 1;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }

        th, td {
            padding: 15px 10px;
            border-bottom: 1px solid #eef0f3;
            vertical-align: top;
        }

        thead th {
            font-weight: 600;
            color: #666;
            background-color: #fafafa;
        }

        tbody tr:hover {
            background-color: #f5f7fa;
        }

        .student-info .name {
            font-weight: 600;
            margin-right: 5px;
        }

        .student-info .code {
            color: #999;
            display: block;
        }

        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 5px;
        }

        .tag-blue {
            background-color: #e9f4ff;
            color: #4a90e2;
        }

        .tag-green {
            background-color: #e4f8f0;
            color: #27ae60;
        }

        .school-info .grade {
            color: #999;
        }

        .status-tag {
            font-weight: 500;
        }

        .status-studying {
            color: #27ae60;
        }

        .status-intention, .status-confirm {
            color: #f39c12;
        }

        .status-self, .status-ended, .status-noneed {
            color: #888;
        }

        .pagination-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .pagination-bar .total {
            color: #666;
        }

        .pagination-nav a, .pagination-nav span {
            display: inline-block;
            margin: 0 2px;
            padding: 6px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            text-decoration: none;
            color: #666;
        }

        .pagination-nav a:hover {
            background-color: #f0f2f5;
        }

        .pagination-nav span.current {
            background-color: #4a90e2;
            color: white;
            border-color: #4a90e2;
        }

        .pagination-nav span.dots {
            border: none;
        }
    </style>
</head>
<body>

<div class="container">
    <!-- 顶部Tabs -->
    <div class="tabs">
        <button class="active">学员</button>
        <button>线索</button>
    </div>

    <!-- 检索表单 -->
    <form action="{{ url()->current() }}" method="GET">
        <div class="filter-bar">
            <input
                type="text"
                name="search[code_name]"
                class="search-input"
                placeholder="搜学员姓名/学号"
                value="{{ $searchParams['code_name'] ?? '' }}"
            >
            <input
                type="text"
                name="search[relation_name]"
                class="search-input"
                placeholder="督导/顾问"
                value="{{ $searchParams['relation_name'] ?? '' }}"
            >
            {{-- 以下为下拉框示例，您需要自行填充选项 --}}
            <select name="search[source]">
                <option value="">生源地</option>
            </select>
            <select name="search[school]">
                <option value="">学校</option>
            </select>
            <select name="search[grade]">
                <option value="">年级</option>
            </select>
            <select name="search[training_status]">
                <option value="">培训状态</option>
            </select>
            <select name="search[study_status]">
                <option value="">升学状态</option>
            </select>
            <select name="search[abroad_intention]">
                <option value="">升学意向</option>
            </select>

            <button type="submit" class="btn btn-primary">查询</button>
            <div class="spacer"></div>
            <button type="button" class="btn btn-primary">新增学员</button>
        </div>
        {{-- 保留分页和排序等现有参数 --}}
        <input type="hidden" name="pageNum" value="{{ $data['page_size'] }}">
    </form>

    <!-- 学员数据表格 -->
    <div class="table-wrapper">
        <table>
            <thead>
            <tr>
                <th style="width: 20px;"><input type="checkbox"></th>
                <th>学员</th>
                <th>当前学校</th>
                <th>培训状态</th>
                <th>升学</th>
                <th>督导/顾问</th>
                <th>基础角色</th>
                <th>权限分享角色</th>
                <th>额外权限角色</th>
            </tr>
            </thead>
            <tbody>
            @forelse ($data['list'] as $student)
            <tr>
                <td><input type="checkbox"></td>
                <td>
                    <div class="student-info">
                        <div>
                            <span class="name">{{ $student['name'] }}</span>
                            <span class="tag tag-blue">{{ $student['region_code'] }}</span>
                        </div>
                        <span class="code">{{ $student['code'] }}</span>
                        {{-- 示例：显示校区信息，需要您的数据中有此字段 --}}
                        {{-- <span class="code">杭州文三校区</span> --}}
                    </div>
                </td>
                <td>
                    <div class="school-info">
                        <div>{{ $student['school_name_cn'] }}</div>
                        <div class="grade">
                            {{-- 将课程体系数组转换为字符串 --}}
                            {{ implode(', ', $student['course_system']) }} {{ $student['grade'] }}
                        </div>
                    </div>
                </td>
                <td>
                    {{-- 根据不同状态设置不同颜色 --}}
                    @php
                    $statusClass = '';
                    switch($student['training_status']) {
                    case '在读': $statusClass = 'status-studying'; break;
                    case '意向': $statusClass = 'status-intention'; break;
                    case '待确认': $statusClass = 'status-confirm'; break;
                    case '自修': $statusClass = 'status-self'; break;
                    case '结束': $statusClass = 'status-ended'; break;
                    case '暂无需求': $statusClass = 'status-noneed'; break;
                    }
                    @endphp
                    <span class="status-tag {{ $statusClass }}">{{ $student['training_status'] }}</span>
                </td>
                <td>
                    <div>{{ $student['abroad_intention'] }}</div>
                    <div class="grade">{{ $student['study_status'] }}</div>
                </td>
                <td>
                    {{-- 将顾问数组转换为逗号分隔的字符串 --}}
                    {{ implode('、', $student['relations']) }}
                </td>
                <td>
                    {{ implode('、', $student['privilege']) }}
                </td>
                <td>
                    {{ implode('、', $student['share_privilege']) }}
                </td>
                <td>
                    {{ implode('、', $student['extra_privilege']) }}
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="6" style="text-align: center; padding: 20px;">暂无数据</td>
            </tr>
            @endforelse
            </tbody>
        </table>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-bar">
        <div class="total">
            共 {{ $data['total'] }} 条
        </div>
        <div class="pagination-nav">
            {{-- 简单分页逻辑 --}}
            @php
            $currentPage = $data['page'];
            $lastPage = $data['total_page'];
            $queryParams = request()->except('page');
            @endphp

            {{-- 上一页 --}}
            <a href="{{ $currentPage > 1 ? url()->current() . '?' . http_build_query(array_merge($queryParams, ['page' => $currentPage - 1])) : '#' }}">&lt;</a>

            {{-- 页码 --}}
            @for ($i = 1; $i <= $lastPage; $i++)
            {{-- 为了简洁，这里只显示当前页码前后几页，您可以扩展此逻辑 --}}
            @if ($i == $currentPage)
            <span class="current">{{ $i }}</span>
            @elseif (abs($i - $currentPage) < 3 || $i <= 2 || $i > $lastPage - 2)
            <a href="{{ url()->current() . '?' . http_build_query(array_merge($queryParams, ['page' => $i])) }}">{{ $i
                }}</a>
            @elseif ($i == 3 || $i == $lastPage - 2)
            <span class="dots">...</span>
            @endif
            @endfor

            {{-- 下一页 --}}
            <a href="{{ $currentPage < $lastPage ? url()->current() . '?' . http_build_query(array_merge($queryParams, ['page' => $currentPage + 1])) : '#' }}">&gt;</a>
        </div>
    </div>

</div>

</body>
</html>
