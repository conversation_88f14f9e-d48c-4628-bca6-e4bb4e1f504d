<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('ua')->get('/version', function () {
    return response()->json([
        'version' => 'V3.0.0_alpha',
        'patch_version' => '20250619',
        'modified_database' => '',
    ]);
});

/*
 * todo 根据伟大的许亮指示
 * todo 跟前端对接传参用蛇形，路由用驼峰
 * todo 谢谢大家
 */

//前端对接用接口

Route::group(['namespace' => 'App\Http\Controllers\PublicControllers', 'middleware' => 'ua'], function ($router) {
    //学校
    $router->resource('school', 'SchoolController', ['only' => ['index', 'show']]);
    //当前用户信息
    $router->get('userDetail', 'UserController@getUserDetail');
    //上传文件
    $router->post('upload/file', 'UploadController@upload');
});

Route::group(['namespace' => 'App\Http\Controllers\EntityControllers', 'middleware' => ['ua', 'crm.permission']], function ($router) {
    //学员
    $router->resource('customer', 'CustomerController');
});


//其他平台对接用接口
