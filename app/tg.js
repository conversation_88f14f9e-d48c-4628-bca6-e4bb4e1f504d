// ==UserScript==
// @name         Telegram 定时消息助手
// @namespace    http://tampermonkey.net/
// @version      2025-08-27
// @description  Telegram 群组定时消息发送和监听工具
// <AUTHOR>
// @match        https://web.telegram.org/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=telegram.org
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_notification
// ==/UserScript==

(function() {
    'use strict';

    // 配置管理类
    class TelegramConfig {
        constructor() {
            this.defaultConfig = {
                groups: [],
                messages: {
                    templates: ['Hello!', '大家好！', '定时提醒'],
                    currentMessage: '',
                    useTemplate: false,
                    enableVariables: true
                },
                schedule: {
                    enabled: false,
                    type: 'interval', // interval, specific, random
                    interval: 60, // 分钟
                    specificTimes: ['09:00', '18:00'],
                    randomMin: 30,
                    randomMax: 120,
                    workdaysOnly: false,
                    timezone: 'Asia/Shanghai'
                },
                monitoring: {
                    enabled: false,
                    keywords: [],
                    users: [],
                    matchMode: 'contains', // exact, contains, regex
                    caseSensitive: false
                },
                security: {
                    confirmBeforeSend: true,
                    maxSendsPerDay: 50,
                    emergencyStop: false
                },
                ui: {
                    theme: 'dark',
                    language: 'zh-CN',
                    position: 'bottom-right'
                }
            };
        }

        load() {
            const saved = GM_getValue('telegram_config', null);
            if (saved) {
                return { ...this.defaultConfig, ...JSON.parse(saved) };
            }
            return this.defaultConfig;
        }

        save(config) {
            GM_setValue('telegram_config', JSON.stringify(config));
        }

        reset() {
            GM_deleteValue('telegram_config');
            return this.defaultConfig;
        }
    }

    // 日志管理类
    class TelegramLogger {
        constructor() {
            this.maxLogs = 1000;
        }

        log(type, message, data = null) {
            const logs = this.getLogs();
            const logEntry = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                type: type, // info, success, error, warning
                message: message,
                data: data
            };

            logs.unshift(logEntry);
            if (logs.length > this.maxLogs) {
                logs.splice(this.maxLogs);
            }

            GM_setValue('telegram_logs', JSON.stringify(logs));
            console.log(`[Telegram助手] ${type.toUpperCase()}: ${message}`, data);
        }

        getLogs() {
            const saved = GM_getValue('telegram_logs', '[]');
            return JSON.parse(saved);
        }

        clearLogs() {
            GM_setValue('telegram_logs', '[]');
        }

        getStats() {
            const logs = this.getLogs();
            const today = new Date().toDateString();
            const todayLogs = logs.filter(log =>
                new Date(log.timestamp).toDateString() === today
            );

            return {
                total: logs.length,
                today: todayLogs.length,
                success: todayLogs.filter(log => log.type === 'success').length,
                errors: todayLogs.filter(log => log.type === 'error').length
            };
        }
    }

    // Telegram 操作类
    class TelegramOperator {
        constructor(logger) {
            this.logger = logger;
            this.currentChat = null;
            this.isMonitoring = false;
            this.scheduleTimer = null;
        }

        // 获取当前聊天信息
        getCurrentChat() {
            const chatTitle = document.querySelector('.chat-title, .peer-title');
            const chatInput = document.querySelector('#editable-message-text, .input-message-input');

            if (chatTitle && chatInput) {
                return {
                    title: chatTitle.textContent.trim(),
                    element: chatTitle,
                    input: chatInput,
                    id: this.extractChatId()
                };
            }
            return null;
        }

        // 提取聊天ID（简化版）
        extractChatId() {
            const url = window.location.href;
            const match = url.match(/\/([^\/]+)$/);
            return match ? match[1] : null;
        }

        // 发送消息
        async sendMessage(text, chatId = null) {
            try {
                const chat = this.getCurrentChat();
                if (!chat) {
                    throw new Error('未找到活动聊天窗口');
                }

                const input = chat.input;

                // 清空输入框
                input.innerHTML = '';
                input.textContent = '';

                // 输入文本
                input.focus();
                document.execCommand('insertText', false, text);

                // 触发输入事件
                input.dispatchEvent(new Event('input', { bubbles: true }));

                // 等待一下让界面更新
                await this.sleep(500);

                // 查找发送按钮
                const sendButton = document.querySelector('.btn-send, .send-button, [data-testid="send-button"]') ||
                                 document.querySelector('button[title*="发送"], button[title*="Send"]');

                if (sendButton && !sendButton.disabled) {
                    sendButton.click();
                    this.logger.log('success', `消息发送成功: ${text}`, { chat: chat.title });
                    return true;
                } else {
                    // 尝试按回车键
                    const enterEvent = new KeyboardEvent('keydown', {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true
                    });
                    input.dispatchEvent(enterEvent);
                    this.logger.log('success', `消息发送成功(回车): ${text}`, { chat: chat.title });
                    return true;
                }
            } catch (error) {
                this.logger.log('error', `消息发送失败: ${error.message}`, { text });
                return false;
            }
        }

        // 处理消息变量
        processMessageVariables(text) {
            const now = new Date();
            const variables = {
                '{time}': now.toLocaleTimeString('zh-CN'),
                '{date}': now.toLocaleDateString('zh-CN'),
                '{datetime}': now.toLocaleString('zh-CN'),
                '{random}': Math.floor(Math.random() * 1000),
                '{timestamp}': now.getTime()
            };

            let processed = text;
            for (const [variable, value] of Object.entries(variables)) {
                processed = processed.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), value);
            }

            return processed;
        }

        // 工具函数：延时
        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    }

    // 调度器类
    class TelegramScheduler {
        constructor(operator, logger) {
            this.operator = operator;
            this.logger = logger;
            this.timers = new Map();
            this.isRunning = false;
        }

        start(config) {
            if (this.isRunning) {
                this.stop();
            }

            this.isRunning = true;
            this.config = config;

            if (config.schedule.type === 'interval') {
                this.startIntervalSchedule(config.schedule.interval);
            } else if (config.schedule.type === 'specific') {
                this.startSpecificTimeSchedule(config.schedule.specificTimes);
            } else if (config.schedule.type === 'random') {
                this.startRandomSchedule(config.schedule.randomMin, config.schedule.randomMax);
            }

            this.logger.log('info', '定时任务已启动', { type: config.schedule.type });
        }

        stop() {
            this.timers.forEach(timer => clearTimeout(timer));
            this.timers.clear();
            this.isRunning = false;
            this.logger.log('info', '定时任务已停止');
        }

        startIntervalSchedule(intervalMinutes) {
            const intervalMs = intervalMinutes * 60 * 1000;

            const scheduleNext = () => {
                if (!this.isRunning) return;

                const timer = setTimeout(async () => {
                    if (this.shouldSendNow()) {
                        await this.sendScheduledMessage();
                    }
                    scheduleNext();
                }, intervalMs);

                this.timers.set('interval', timer);
            };

            scheduleNext();
        }

        startSpecificTimeSchedule(times) {
            const scheduleForTime = (timeStr) => {
                const [hours, minutes] = timeStr.split(':').map(Number);
                const now = new Date();
                const targetTime = new Date();
                targetTime.setHours(hours, minutes, 0, 0);

                if (targetTime <= now) {
                    targetTime.setDate(targetTime.getDate() + 1);
                }

                const delay = targetTime.getTime() - now.getTime();

                const timer = setTimeout(async () => {
                    if (this.shouldSendNow()) {
                        await this.sendScheduledMessage();
                    }
                    scheduleForTime(timeStr); // 重新安排下一天
                }, delay);

                this.timers.set(`specific_${timeStr}`, timer);
            };

            times.forEach(time => scheduleForTime(time));
        }

        startRandomSchedule(minMinutes, maxMinutes) {
            const scheduleNext = () => {
                if (!this.isRunning) return;

                const randomMinutes = Math.floor(Math.random() * (maxMinutes - minMinutes + 1)) + minMinutes;
                const delay = randomMinutes * 60 * 1000;

                const timer = setTimeout(async () => {
                    if (this.shouldSendNow()) {
                        await this.sendScheduledMessage();
                    }
                    scheduleNext();
                }, delay);

                this.timers.set('random', timer);
            };

            scheduleNext();
        }

        shouldSendNow() {
            if (this.config.security.emergencyStop) {
                return false;
            }

            if (this.config.schedule.workdaysOnly) {
                const now = new Date();
                const dayOfWeek = now.getDay();
                if (dayOfWeek === 0 || dayOfWeek === 6) { // 周末
                    return false;
                }
            }

            // 检查每日发送限制
            const stats = this.logger.getStats();
            if (stats.success >= this.config.security.maxSendsPerDay) {
                this.logger.log('warning', '已达到每日发送限制');
                return false;
            }

            return true;
        }

        async sendScheduledMessage() {
            try {
                let message = this.config.messages.currentMessage;

                if (this.config.messages.useTemplate && this.config.messages.templates.length > 0) {
                    const randomTemplate = this.config.messages.templates[
                        Math.floor(Math.random() * this.config.messages.templates.length)
                    ];
                    message = randomTemplate;
                }

                if (this.config.messages.enableVariables) {
                    message = this.operator.processMessageVariables(message);
                }

                if (this.config.security.confirmBeforeSend) {
                    const confirmed = confirm(`确认发送消息: "${message}"?`);
                    if (!confirmed) {
                        this.logger.log('info', '用户取消发送消息');
                        return;
                    }
                }

                await this.operator.sendMessage(message);
            } catch (error) {
                this.logger.log('error', `定时发送失败: ${error.message}`);
            }
        }
    }

    // UI界面类
    class TelegramUI {
        constructor(config, logger, operator, scheduler) {
            this.config = config;
            this.logger = logger;
            this.operator = operator;
            this.scheduler = scheduler;
            this.isVisible = false;
            this.currentTab = 'groups';
        }

        // 创建主界面
        createUI() {
            if (document.getElementById('telegram-assistant-ui')) {
                return; // 已存在
            }

            const ui = document.createElement('div');
            ui.id = 'telegram-assistant-ui';
            ui.innerHTML = this.getUIHTML();
            document.body.appendChild(ui);

            this.attachEventListeners();
            this.updateUI();
        }

        // 获取UI HTML
        getUIHTML() {
            return `
                <div class="tg-assistant-container">
                    <div class="tg-assistant-header">
                        <h3>📱 Telegram 助手</h3>
                        <div class="tg-assistant-controls">
                            <button id="tg-minimize" title="最小化">−</button>
                            <button id="tg-close" title="关闭">×</button>
                        </div>
                    </div>

                    <div class="tg-assistant-tabs">
                        <button class="tg-tab active" data-tab="groups">群组</button>
                        <button class="tg-tab" data-tab="messages">消息</button>
                        <button class="tg-tab" data-tab="schedule">定时</button>
                        <button class="tg-tab" data-tab="monitor">监听</button>
                        <button class="tg-tab" data-tab="logs">日志</button>
                    </div>

                    <div class="tg-assistant-content">
                        ${this.getGroupsTabHTML()}
                        ${this.getMessagesTabHTML()}
                        ${this.getScheduleTabHTML()}
                        ${this.getMonitorTabHTML()}
                        ${this.getLogsTabHTML()}
                    </div>

                    <div class="tg-assistant-footer">
                        <div class="tg-status">
                            <span id="tg-status-text">就绪</span>
                            <div class="tg-status-indicator" id="tg-status-indicator"></div>
                        </div>
                        <div class="tg-actions">
                            <button id="tg-emergency-stop" class="tg-btn-danger">紧急停止</button>
                            <button id="tg-toggle-schedule" class="tg-btn-primary">启动定时</button>
                        </div>
                    </div>
                </div>

                <div class="tg-assistant-trigger" id="tg-assistant-trigger">
                    📱
                </div>

                <style>
                    ${this.getUICSS()}
                </style>
            `;
        }

        // 群组标签页HTML
        getGroupsTabHTML() {
            return `
                <div class="tg-tab-content active" data-tab="groups">
                    <div class="tg-section">
                        <h4>当前群组</h4>
                        <div id="tg-current-group" class="tg-current-group">
                            <span>未检测到活动群组</span>
                        </div>
                    </div>

                    <div class="tg-section">
                        <h4>已保存的群组</h4>
                        <div id="tg-saved-groups" class="tg-saved-groups">
                            <!-- 动态生成 -->
                        </div>
                        <button id="tg-add-current-group" class="tg-btn-secondary">添加当前群组</button>
                    </div>
                </div>
            `;
        }

        // 消息标签页HTML
        getMessagesTabHTML() {
            return `
                <div class="tg-tab-content" data-tab="messages">
                    <div class="tg-section">
                        <h4>消息内容</h4>
                        <textarea id="tg-message-text" placeholder="输入要发送的消息..." rows="3"></textarea>
                        <div class="tg-checkbox-group">
                            <label>
                                <input type="checkbox" id="tg-use-template"> 使用随机模板
                            </label>
                            <label>
                                <input type="checkbox" id="tg-enable-variables"> 启用变量替换
                            </label>
                        </div>
                    </div>

                    <div class="tg-section">
                        <h4>消息模板</h4>
                        <div id="tg-templates" class="tg-templates">
                            <!-- 动态生成 -->
                        </div>
                        <div class="tg-input-group">
                            <input type="text" id="tg-new-template" placeholder="添加新模板...">
                            <button id="tg-add-template" class="tg-btn-secondary">添加</button>
                        </div>
                    </div>

                    <div class="tg-section">
                        <h4>变量说明</h4>
                        <div class="tg-variables-help">
                            <code>{time}</code> - 当前时间<br>
                            <code>{date}</code> - 当前日期<br>
                            <code>{datetime}</code> - 日期时间<br>
                            <code>{random}</code> - 随机数<br>
                            <code>{timestamp}</code> - 时间戳
                        </div>
                    </div>

                    <div class="tg-section">
                        <button id="tg-test-send" class="tg-btn-primary">测试发送</button>
                    </div>
                </div>
            `;
        }

        // 定时标签页HTML
        getScheduleTabHTML() {
            return `
                <div class="tg-tab-content" data-tab="schedule">
                    <div class="tg-section">
                        <h4>定时类型</h4>
                        <div class="tg-radio-group">
                            <label>
                                <input type="radio" name="schedule-type" value="interval" checked> 固定间隔
                            </label>
                            <label>
                                <input type="radio" name="schedule-type" value="specific"> 指定时间
                            </label>
                            <label>
                                <input type="radio" name="schedule-type" value="random"> 随机间隔
                            </label>
                        </div>
                    </div>

                    <div class="tg-section" id="tg-interval-config">
                        <h4>间隔设置</h4>
                        <div class="tg-input-group">
                            <input type="number" id="tg-interval" min="1" max="1440" value="60">
                            <span>分钟</span>
                        </div>
                    </div>

                    <div class="tg-section" id="tg-specific-config" style="display: none;">
                        <h4>指定时间</h4>
                        <div id="tg-specific-times" class="tg-specific-times">
                            <!-- 动态生成 -->
                        </div>
                        <div class="tg-input-group">
                            <input type="time" id="tg-new-time">
                            <button id="tg-add-time" class="tg-btn-secondary">添加</button>
                        </div>
                    </div>

                    <div class="tg-section" id="tg-random-config" style="display: none;">
                        <h4>随机间隔</h4>
                        <div class="tg-input-group">
                            <input type="number" id="tg-random-min" min="1" value="30">
                            <span>到</span>
                            <input type="number" id="tg-random-max" min="1" value="120">
                            <span>分钟</span>
                        </div>
                    </div>

                    <div class="tg-section">
                        <h4>高级选项</h4>
                        <div class="tg-checkbox-group">
                            <label>
                                <input type="checkbox" id="tg-workdays-only"> 仅工作日
                            </label>
                            <label>
                                <input type="checkbox" id="tg-confirm-send"> 发送前确认
                            </label>
                        </div>
                        <div class="tg-input-group">
                            <label>每日最大发送数:</label>
                            <input type="number" id="tg-max-sends" min="1" max="1000" value="50">
                        </div>
                    </div>
                </div>
            `;
        }

        // 监听标签页HTML
        getMonitorTabHTML() {
            return `
                <div class="tg-tab-content" data-tab="monitor">
                    <div class="tg-section">
                        <h4>关键词监听</h4>
                        <div class="tg-checkbox-group">
                            <label>
                                <input type="checkbox" id="tg-enable-monitor"> 启用监听
                            </label>
                            <label>
                                <input type="checkbox" id="tg-case-sensitive"> 区分大小写
                            </label>
                        </div>
                        <div class="tg-radio-group">
                            <label>
                                <input type="radio" name="match-mode" value="contains" checked> 包含
                            </label>
                            <label>
                                <input type="radio" name="match-mode" value="exact"> 完全匹配
                            </label>
                            <label>
                                <input type="radio" name="match-mode" value="regex"> 正则表达式
                            </label>
                        </div>
                    </div>

                    <div class="tg-section">
                        <h4>关键词列表</h4>
                        <div id="tg-keywords" class="tg-keywords">
                            <!-- 动态生成 -->
                        </div>
                        <div class="tg-input-group">
                            <input type="text" id="tg-new-keyword" placeholder="添加关键词...">
                            <button id="tg-add-keyword" class="tg-btn-secondary">添加</button>
                        </div>
                    </div>

                    <div class="tg-section">
                        <h4>用户监听</h4>
                        <div id="tg-monitor-users" class="tg-monitor-users">
                            <!-- 动态生成 -->
                        </div>
                        <div class="tg-input-group">
                            <input type="text" id="tg-new-user" placeholder="添加用户名...">
                            <button id="tg-add-user" class="tg-btn-secondary">添加</button>
                        </div>
                    </div>
                </div>
            `;
        }

        // 日志标签页HTML
        getLogsTabHTML() {
            return `
                <div class="tg-tab-content" data-tab="logs">
                    <div class="tg-section">
                        <h4>统计信息</h4>
                        <div id="tg-stats" class="tg-stats">
                            <!-- 动态生成 -->
                        </div>
                    </div>

                    <div class="tg-section">
                        <div class="tg-section-header">
                            <h4>操作日志</h4>
                            <button id="tg-clear-logs" class="tg-btn-secondary">清空日志</button>
                        </div>
                        <div id="tg-logs-list" class="tg-logs-list">
                            <!-- 动态生成 -->
                        </div>
                    </div>
                </div>
            `;
        }

        // CSS样式
        getUICSS() {
            return `
                .tg-assistant-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 400px;
                    max-height: 600px;
                    background: #1e1e1e;
                    border: 1px solid #333;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 14px;
                    color: #fff;
                    z-index: 10000;
                    display: none;
                }

                .tg-assistant-container.visible {
                    display: block;
                }

                .tg-assistant-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 12px 16px;
                    background: #2a2a2a;
                    border-bottom: 1px solid #333;
                    border-radius: 8px 8px 0 0;
                }

                .tg-assistant-header h3 {
                    margin: 0;
                    font-size: 16px;
                    font-weight: 600;
                }

                .tg-assistant-controls button {
                    background: none;
                    border: none;
                    color: #ccc;
                    font-size: 18px;
                    cursor: pointer;
                    padding: 4px 8px;
                    margin-left: 4px;
                    border-radius: 4px;
                }

                .tg-assistant-controls button:hover {
                    background: #444;
                    color: #fff;
                }

                .tg-assistant-tabs {
                    display: flex;
                    background: #2a2a2a;
                    border-bottom: 1px solid #333;
                }

                .tg-tab {
                    flex: 1;
                    padding: 10px 8px;
                    background: none;
                    border: none;
                    color: #ccc;
                    cursor: pointer;
                    font-size: 12px;
                    transition: all 0.2s;
                }

                .tg-tab:hover {
                    background: #333;
                    color: #fff;
                }

                .tg-tab.active {
                    background: #0088cc;
                    color: #fff;
                }

                .tg-assistant-content {
                    max-height: 400px;
                    overflow-y: auto;
                    padding: 16px;
                }

                .tg-tab-content {
                    display: none;
                }

                .tg-tab-content.active {
                    display: block;
                }

                .tg-section {
                    margin-bottom: 16px;
                }

                .tg-section h4 {
                    margin: 0 0 8px 0;
                    font-size: 14px;
                    font-weight: 600;
                    color: #0088cc;
                }

                .tg-section-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 8px;
                }

                .tg-input-group {
                    display: flex;
                    gap: 8px;
                    align-items: center;
                    margin-bottom: 8px;
                }

                .tg-input-group input {
                    flex: 1;
                    padding: 8px;
                    background: #333;
                    border: 1px solid #555;
                    border-radius: 4px;
                    color: #fff;
                    font-size: 14px;
                }

                .tg-input-group input:focus {
                    outline: none;
                    border-color: #0088cc;
                }

                textarea {
                    width: 100%;
                    padding: 8px;
                    background: #333;
                    border: 1px solid #555;
                    border-radius: 4px;
                    color: #fff;
                    font-size: 14px;
                    resize: vertical;
                    font-family: inherit;
                }

                textarea:focus {
                    outline: none;
                    border-color: #0088cc;
                }

                .tg-checkbox-group, .tg-radio-group {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                    margin-bottom: 12px;
                }

                .tg-checkbox-group label, .tg-radio-group label {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    cursor: pointer;
                    font-size: 13px;
                }

                .tg-btn-primary, .tg-btn-secondary, .tg-btn-danger {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 13px;
                    font-weight: 500;
                    transition: all 0.2s;
                }

                .tg-btn-primary {
                    background: #0088cc;
                    color: #fff;
                }

                .tg-btn-primary:hover {
                    background: #0077bb;
                }

                .tg-btn-secondary {
                    background: #555;
                    color: #fff;
                }

                .tg-btn-secondary:hover {
                    background: #666;
                }

                .tg-btn-danger {
                    background: #dc3545;
                    color: #fff;
                }

                .tg-btn-danger:hover {
                    background: #c82333;
                }

                .tg-assistant-footer {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 12px 16px;
                    background: #2a2a2a;
                    border-top: 1px solid #333;
                    border-radius: 0 0 8px 8px;
                }

                .tg-status {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 12px;
                }

                .tg-status-indicator {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background: #28a745;
                }

                .tg-status-indicator.warning {
                    background: #ffc107;
                }

                .tg-status-indicator.error {
                    background: #dc3545;
                }

                .tg-actions {
                    display: flex;
                    gap: 8px;
                }

                .tg-assistant-trigger {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    width: 50px;
                    height: 50px;
                    background: #0088cc;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 20px;
                    cursor: pointer;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                    z-index: 9999;
                    transition: all 0.2s;
                }

                .tg-assistant-trigger:hover {
                    background: #0077bb;
                    transform: scale(1.1);
                }

                .tg-current-group {
                    padding: 12px;
                    background: #333;
                    border-radius: 4px;
                    margin-bottom: 12px;
                }

                .tg-saved-groups, .tg-templates, .tg-keywords, .tg-monitor-users, .tg-specific-times {
                    max-height: 120px;
                    overflow-y: auto;
                    margin-bottom: 8px;
                }

                .tg-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px;
                    background: #333;
                    border-radius: 4px;
                    margin-bottom: 4px;
                }

                .tg-item button {
                    background: #dc3545;
                    border: none;
                    color: #fff;
                    padding: 4px 8px;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 11px;
                }

                .tg-item button:hover {
                    background: #c82333;
                }

                .tg-variables-help {
                    background: #333;
                    padding: 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    line-height: 1.5;
                }

                .tg-variables-help code {
                    background: #555;
                    padding: 2px 4px;
                    border-radius: 2px;
                    font-family: 'Courier New', monospace;
                }

                .tg-stats {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 12px;
                    margin-bottom: 16px;
                }

                .tg-stat-item {
                    background: #333;
                    padding: 12px;
                    border-radius: 4px;
                    text-align: center;
                }

                .tg-stat-value {
                    font-size: 20px;
                    font-weight: bold;
                    color: #0088cc;
                }

                .tg-stat-label {
                    font-size: 11px;
                    color: #ccc;
                    margin-top: 4px;
                }

                .tg-logs-list {
                    max-height: 200px;
                    overflow-y: auto;
                }

                .tg-log-item {
                    padding: 8px;
                    border-left: 3px solid #0088cc;
                    background: #333;
                    margin-bottom: 4px;
                    border-radius: 0 4px 4px 0;
                    font-size: 12px;
                }

                .tg-log-item.success {
                    border-left-color: #28a745;
                }

                .tg-log-item.error {
                    border-left-color: #dc3545;
                }

                .tg-log-item.warning {
                    border-left-color: #ffc107;
                }

                .tg-log-time {
                    color: #888;
                    font-size: 10px;
                }

                .tg-log-message {
                    margin-top: 2px;
                }

                /* 滚动条样式 */
                .tg-assistant-content::-webkit-scrollbar,
                .tg-saved-groups::-webkit-scrollbar,
                .tg-templates::-webkit-scrollbar,
                .tg-keywords::-webkit-scrollbar,
                .tg-logs-list::-webkit-scrollbar {
                    width: 6px;
                }

                .tg-assistant-content::-webkit-scrollbar-track,
                .tg-saved-groups::-webkit-scrollbar-track,
                .tg-templates::-webkit-scrollbar-track,
                .tg-keywords::-webkit-scrollbar-track,
                .tg-logs-list::-webkit-scrollbar-track {
                    background: #2a2a2a;
                }

                .tg-assistant-content::-webkit-scrollbar-thumb,
                .tg-saved-groups::-webkit-scrollbar-thumb,
                .tg-templates::-webkit-scrollbar-thumb,
                .tg-keywords::-webkit-scrollbar-thumb,
                .tg-logs-list::-webkit-scrollbar-thumb {
                    background: #555;
                    border-radius: 3px;
                }

                .tg-assistant-content::-webkit-scrollbar-thumb:hover,
                .tg-saved-groups::-webkit-scrollbar-thumb:hover,
                .tg-templates::-webkit-scrollbar-thumb:hover,
                .tg-keywords::-webkit-scrollbar-thumb:hover,
                .tg-logs-list::-webkit-scrollbar-thumb:hover {
                    background: #666;
                }
            `;
        }

        // 附加事件监听器
        attachEventListeners() {
            // 触发器点击
            document.getElementById('tg-assistant-trigger').addEventListener('click', () => {
                this.toggleUI();
            });

            // 关闭和最小化
            document.getElementById('tg-close').addEventListener('click', () => {
                this.hideUI();
            });

            document.getElementById('tg-minimize').addEventListener('click', () => {
                this.hideUI();
            });

            // 标签页切换
            document.querySelectorAll('.tg-tab').forEach(tab => {
                tab.addEventListener('click', (e) => {
                    this.switchTab(e.target.dataset.tab);
                });
            });

            // 紧急停止
            document.getElementById('tg-emergency-stop').addEventListener('click', () => {
                this.emergencyStop();
            });

            // 启动/停止定时
            document.getElementById('tg-toggle-schedule').addEventListener('click', () => {
                this.toggleSchedule();
            });

            // 添加当前群组
            document.getElementById('tg-add-current-group').addEventListener('click', () => {
                this.addCurrentGroup();
            });

            // 测试发送
            document.getElementById('tg-test-send').addEventListener('click', () => {
                this.testSend();
            });

            // 添加模板
            document.getElementById('tg-add-template').addEventListener('click', () => {
                this.addTemplate();
            });

            // 定时类型切换
            document.querySelectorAll('input[name="schedule-type"]').forEach(radio => {
                radio.addEventListener('change', (e) => {
                    this.switchScheduleType(e.target.value);
                });
            });

            // 添加时间
            document.getElementById('tg-add-time').addEventListener('click', () => {
                this.addSpecificTime();
            });

            // 添加关键词
            document.getElementById('tg-add-keyword').addEventListener('click', () => {
                this.addKeyword();
            });

            // 添加用户
            document.getElementById('tg-add-user').addEventListener('click', () => {
                this.addMonitorUser();
            });

            // 清空日志
            document.getElementById('tg-clear-logs').addEventListener('click', () => {
                this.clearLogs();
            });

            // 配置变化监听
            this.attachConfigListeners();
        }

        // 附加配置监听器
        attachConfigListeners() {
            // 消息配置
            document.getElementById('tg-message-text').addEventListener('input', (e) => {
                this.config.messages.currentMessage = e.target.value;
                this.saveConfig();
            });

            document.getElementById('tg-use-template').addEventListener('change', (e) => {
                this.config.messages.useTemplate = e.target.checked;
                this.saveConfig();
            });

            document.getElementById('tg-enable-variables').addEventListener('change', (e) => {
                this.config.messages.enableVariables = e.target.checked;
                this.saveConfig();
            });

            // 定时配置
            document.getElementById('tg-interval').addEventListener('input', (e) => {
                this.config.schedule.interval = parseInt(e.target.value);
                this.saveConfig();
            });

            document.getElementById('tg-random-min').addEventListener('input', (e) => {
                this.config.schedule.randomMin = parseInt(e.target.value);
                this.saveConfig();
            });

            document.getElementById('tg-random-max').addEventListener('input', (e) => {
                this.config.schedule.randomMax = parseInt(e.target.value);
                this.saveConfig();
            });

            document.getElementById('tg-workdays-only').addEventListener('change', (e) => {
                this.config.schedule.workdaysOnly = e.target.checked;
                this.saveConfig();
            });

            document.getElementById('tg-confirm-send').addEventListener('change', (e) => {
                this.config.security.confirmBeforeSend = e.target.checked;
                this.saveConfig();
            });

            document.getElementById('tg-max-sends').addEventListener('input', (e) => {
                this.config.security.maxSendsPerDay = parseInt(e.target.value);
                this.saveConfig();
            });

            // 监听配置
            document.getElementById('tg-enable-monitor').addEventListener('change', (e) => {
                this.config.monitoring.enabled = e.target.checked;
                this.saveConfig();
            });

            document.getElementById('tg-case-sensitive').addEventListener('change', (e) => {
                this.config.monitoring.caseSensitive = e.target.checked;
                this.saveConfig();
            });

            document.querySelectorAll('input[name="match-mode"]').forEach(radio => {
                radio.addEventListener('change', (e) => {
                    this.config.monitoring.matchMode = e.target.value;
                    this.saveConfig();
                });
            });
        }

        // UI控制方法
        toggleUI() {
            const container = document.getElementById('telegram-assistant-ui').querySelector('.tg-assistant-container');
            if (container.classList.contains('visible')) {
                this.hideUI();
            } else {
                this.showUI();
            }
        }

        showUI() {
            const container = document.getElementById('telegram-assistant-ui').querySelector('.tg-assistant-container');
            container.classList.add('visible');
            this.isVisible = true;
            this.updateUI();
        }

        hideUI() {
            const container = document.getElementById('telegram-assistant-ui').querySelector('.tg-assistant-container');
            container.classList.remove('visible');
            this.isVisible = false;
        }

        switchTab(tabName) {
            // 更新标签页
            document.querySelectorAll('.tg-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 更新内容
            document.querySelectorAll('.tg-tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelector(`.tg-tab-content[data-tab="${tabName}"]`).classList.add('active');

            this.currentTab = tabName;
            this.updateTabContent(tabName);
        }

        // 更新UI内容
        updateUI() {
            this.updateCurrentGroup();
            this.updateSavedGroups();
            this.updateTemplates();
            this.updateScheduleConfig();
            this.updateMonitorConfig();
            this.updateStats();
            this.updateLogs();
            this.updateStatus();
        }

        updateCurrentGroup() {
            const currentGroupEl = document.getElementById('tg-current-group');
            const chat = this.operator.getCurrentChat();

            if (chat) {
                currentGroupEl.innerHTML = `
                    <div><strong>${chat.title}</strong></div>
                    <div style="font-size: 12px; color: #888;">ID: ${chat.id || '未知'}</div>
                `;
            } else {
                currentGroupEl.innerHTML = '<span>未检测到活动群组</span>';
            }
        }

        updateSavedGroups() {
            const container = document.getElementById('tg-saved-groups');
            container.innerHTML = '';

            this.config.groups.forEach((group, index) => {
                const item = document.createElement('div');
                item.className = 'tg-item';
                item.innerHTML = `
                    <span>${group.title}</span>
                    <button onclick="telegramApp.removeGroup(${index})">删除</button>
                `;
                container.appendChild(item);
            });
        }

        updateTemplates() {
            const container = document.getElementById('tg-templates');
            container.innerHTML = '';

            this.config.messages.templates.forEach((template, index) => {
                const item = document.createElement('div');
                item.className = 'tg-item';
                item.innerHTML = `
                    <span>${template}</span>
                    <button onclick="telegramApp.removeTemplate(${index})">删除</button>
                `;
                container.appendChild(item);
            });
        }

        updateScheduleConfig() {
            // 更新表单值
            document.getElementById('tg-message-text').value = this.config.messages.currentMessage;
            document.getElementById('tg-use-template').checked = this.config.messages.useTemplate;
            document.getElementById('tg-enable-variables').checked = this.config.messages.enableVariables;

            document.getElementById('tg-interval').value = this.config.schedule.interval;
            document.getElementById('tg-random-min').value = this.config.schedule.randomMin;
            document.getElementById('tg-random-max').value = this.config.schedule.randomMax;
            document.getElementById('tg-workdays-only').checked = this.config.schedule.workdaysOnly;
            document.getElementById('tg-confirm-send').checked = this.config.security.confirmBeforeSend;
            document.getElementById('tg-max-sends').value = this.config.security.maxSendsPerDay;

            // 更新定时类型
            document.querySelector(`input[name="schedule-type"][value="${this.config.schedule.type}"]`).checked = true;
            this.switchScheduleType(this.config.schedule.type);

            // 更新指定时间列表
            this.updateSpecificTimes();
        }

        updateSpecificTimes() {
            const container = document.getElementById('tg-specific-times');
            container.innerHTML = '';

            this.config.schedule.specificTimes.forEach((time, index) => {
                const item = document.createElement('div');
                item.className = 'tg-item';
                item.innerHTML = `
                    <span>${time}</span>
                    <button onclick="telegramApp.removeSpecificTime(${index})">删除</button>
                `;
                container.appendChild(item);
            });
        }

        updateMonitorConfig() {
            document.getElementById('tg-enable-monitor').checked = this.config.monitoring.enabled;
            document.getElementById('tg-case-sensitive').checked = this.config.monitoring.caseSensitive;
            document.querySelector(`input[name="match-mode"][value="${this.config.monitoring.matchMode}"]`).checked = true;

            // 更新关键词列表
            const keywordsContainer = document.getElementById('tg-keywords');
            keywordsContainer.innerHTML = '';

            this.config.monitoring.keywords.forEach((keyword, index) => {
                const item = document.createElement('div');
                item.className = 'tg-item';
                item.innerHTML = `
                    <span>${keyword}</span>
                    <button onclick="telegramApp.removeKeyword(${index})">删除</button>
                `;
                keywordsContainer.appendChild(item);
            });

            // 更新用户列表
            const usersContainer = document.getElementById('tg-monitor-users');
            usersContainer.innerHTML = '';

            this.config.monitoring.users.forEach((user, index) => {
                const item = document.createElement('div');
                item.className = 'tg-item';
                item.innerHTML = `
                    <span>${user}</span>
                    <button onclick="telegramApp.removeMonitorUser(${index})">删除</button>
                `;
                usersContainer.appendChild(item);
            });
        }

        updateStats() {
            const stats = this.logger.getStats();
            const container = document.getElementById('tg-stats');

            container.innerHTML = `
                <div class="tg-stat-item">
                    <div class="tg-stat-value">${stats.today}</div>
                    <div class="tg-stat-label">今日发送</div>
                </div>
                <div class="tg-stat-item">
                    <div class="tg-stat-value">${stats.success}</div>
                    <div class="tg-stat-label">成功次数</div>
                </div>
                <div class="tg-stat-item">
                    <div class="tg-stat-value">${stats.errors}</div>
                    <div class="tg-stat-label">失败次数</div>
                </div>
                <div class="tg-stat-item">
                    <div class="tg-stat-value">${stats.total}</div>
                    <div class="tg-stat-label">总日志数</div>
                </div>
            `;
        }

        updateLogs() {
            const logs = this.logger.getLogs().slice(0, 50); // 只显示最近50条
            const container = document.getElementById('tg-logs-list');

            container.innerHTML = '';

            logs.forEach(log => {
                const item = document.createElement('div');
                item.className = `tg-log-item ${log.type}`;
                item.innerHTML = `
                    <div class="tg-log-time">${new Date(log.timestamp).toLocaleString('zh-CN')}</div>
                    <div class="tg-log-message">${log.message}</div>
                `;
                container.appendChild(item);
            });
        }

        updateStatus() {
            const statusText = document.getElementById('tg-status-text');
            const statusIndicator = document.getElementById('tg-status-indicator');
            const toggleButton = document.getElementById('tg-toggle-schedule');

            if (this.config.security.emergencyStop) {
                statusText.textContent = '紧急停止';
                statusIndicator.className = 'tg-status-indicator error';
                toggleButton.textContent = '启动定时';
                toggleButton.disabled = true;
            } else if (this.scheduler.isRunning) {
                statusText.textContent = '定时运行中';
                statusIndicator.className = 'tg-status-indicator';
                toggleButton.textContent = '停止定时';
                toggleButton.disabled = false;
            } else {
                statusText.textContent = '就绪';
                statusIndicator.className = 'tg-status-indicator';
                toggleButton.textContent = '启动定时';
                toggleButton.disabled = false;
            }
        }

        // 事件处理方法
        switchScheduleType(type) {
            document.getElementById('tg-interval-config').style.display = type === 'interval' ? 'block' : 'none';
            document.getElementById('tg-specific-config').style.display = type === 'specific' ? 'block' : 'none';
            document.getElementById('tg-random-config').style.display = type === 'random' ? 'block' : 'none';

            this.config.schedule.type = type;
            this.saveConfig();
        }

        emergencyStop() {
            this.config.security.emergencyStop = true;
            this.scheduler.stop();
            this.saveConfig();
            this.updateStatus();
            this.logger.log('warning', '用户触发紧急停止');

            GM_notification({
                title: 'Telegram助手',
                text: '紧急停止已激活',
                timeout: 3000
            });
        }

        toggleSchedule() {
            if (this.config.security.emergencyStop) {
                this.config.security.emergencyStop = false;
                this.saveConfig();
                this.updateStatus();
                return;
            }

            if (this.scheduler.isRunning) {
                this.scheduler.stop();
                this.logger.log('info', '用户停止定时任务');
            } else {
                if (!this.config.messages.currentMessage && !this.config.messages.useTemplate) {
                    alert('请先设置消息内容或启用模板');
                    return;
                }

                this.scheduler.start(this.config);
                this.logger.log('info', '用户启动定时任务');
            }

            this.updateStatus();
        }

        addCurrentGroup() {
            const chat = this.operator.getCurrentChat();
            if (!chat) {
                alert('未检测到活动群组');
                return;
            }

            const exists = this.config.groups.some(group => group.id === chat.id);
            if (exists) {
                alert('该群组已存在');
                return;
            }

            this.config.groups.push({
                id: chat.id,
                title: chat.title,
                addedAt: new Date().toISOString()
            });

            this.saveConfig();
            this.updateSavedGroups();
            this.logger.log('success', `添加群组: ${chat.title}`);
        }

        async testSend() {
            let message = document.getElementById('tg-message-text').value;

            if (!message) {
                if (this.config.messages.useTemplate && this.config.messages.templates.length > 0) {
                    message = this.config.messages.templates[0];
                } else {
                    alert('请输入测试消息');
                    return;
                }
            }

            if (this.config.messages.enableVariables) {
                message = this.operator.processMessageVariables(message);
            }

            const success = await this.operator.sendMessage(message);
            if (success) {
                alert('测试消息发送成功！');
            } else {
                alert('测试消息发送失败，请检查日志');
            }
        }

        addTemplate() {
            const input = document.getElementById('tg-new-template');
            const template = input.value.trim();

            if (!template) {
                alert('请输入模板内容');
                return;
            }

            if (this.config.messages.templates.includes(template)) {
                alert('该模板已存在');
                return;
            }

            this.config.messages.templates.push(template);
            input.value = '';
            this.saveConfig();
            this.updateTemplates();
            this.logger.log('success', `添加模板: ${template}`);
        }

        addSpecificTime() {
            const input = document.getElementById('tg-new-time');
            const time = input.value;

            if (!time) {
                alert('请选择时间');
                return;
            }

            if (this.config.schedule.specificTimes.includes(time)) {
                alert('该时间已存在');
                return;
            }

            this.config.schedule.specificTimes.push(time);
            input.value = '';
            this.saveConfig();
            this.updateSpecificTimes();
            this.logger.log('success', `添加定时: ${time}`);
        }

        addKeyword() {
            const input = document.getElementById('tg-new-keyword');
            const keyword = input.value.trim();

            if (!keyword) {
                alert('请输入关键词');
                return;
            }

            if (this.config.monitoring.keywords.includes(keyword)) {
                alert('该关键词已存在');
                return;
            }

            this.config.monitoring.keywords.push(keyword);
            input.value = '';
            this.saveConfig();
            this.updateMonitorConfig();
            this.logger.log('success', `添加关键词: ${keyword}`);
        }

        addMonitorUser() {
            const input = document.getElementById('tg-new-user');
            const user = input.value.trim();

            if (!user) {
                alert('请输入用户名');
                return;
            }

            if (this.config.monitoring.users.includes(user)) {
                alert('该用户已存在');
                return;
            }

            this.config.monitoring.users.push(user);
            input.value = '';
            this.saveConfig();
            this.updateMonitorConfig();
            this.logger.log('success', `添加监听用户: ${user}`);
        }

        clearLogs() {
            if (confirm('确认清空所有日志？')) {
                this.logger.clearLogs();
                this.updateLogs();
                this.updateStats();
            }
        }

        // 删除方法
        removeGroup(index) {
            const group = this.config.groups[index];
            this.config.groups.splice(index, 1);
            this.saveConfig();
            this.updateSavedGroups();
            this.logger.log('info', `删除群组: ${group.title}`);
        }

        removeTemplate(index) {
            const template = this.config.messages.templates[index];
            this.config.messages.templates.splice(index, 1);
            this.saveConfig();
            this.updateTemplates();
            this.logger.log('info', `删除模板: ${template}`);
        }

        removeSpecificTime(index) {
            const time = this.config.schedule.specificTimes[index];
            this.config.schedule.specificTimes.splice(index, 1);
            this.saveConfig();
            this.updateSpecificTimes();
            this.logger.log('info', `删除定时: ${time}`);
        }

        removeKeyword(index) {
            const keyword = this.config.monitoring.keywords[index];
            this.config.monitoring.keywords.splice(index, 1);
            this.saveConfig();
            this.updateMonitorConfig();
            this.logger.log('info', `删除关键词: ${keyword}`);
        }

        removeMonitorUser(index) {
            const user = this.config.monitoring.users[index];
            this.config.monitoring.users.splice(index, 1);
            this.saveConfig();
            this.updateMonitorConfig();
            this.logger.log('info', `删除监听用户: ${user}`);
        }

        // 配置管理
        saveConfig() {
            const configManager = new TelegramConfig();
            configManager.save(this.config);
        }

        updateTabContent(tabName) {
            if (tabName === 'logs') {
                this.updateStats();
                this.updateLogs();
            }
        }
    }

    // 主应用类
    class TelegramApp {
        constructor() {
            this.configManager = new TelegramConfig();
            this.config = this.configManager.load();
            this.logger = new TelegramLogger();
            this.operator = new TelegramOperator(this.logger);
            this.scheduler = new TelegramScheduler(this.operator, this.logger);
            this.ui = new TelegramUI(this.config, this.logger, this.operator, this.scheduler);

            this.isInitialized = false;
            this.monitorInterval = null;
        }

        // 初始化应用
        async init() {
            if (this.isInitialized) return;

            try {
                // 等待页面加载完成
                await this.waitForPageLoad();

                // 创建UI
                this.ui.createUI();

                // 启动消息监听
                if (this.config.monitoring.enabled) {
                    this.startMessageMonitoring();
                }

                // 如果之前有定时任务在运行，恢复它
                if (this.config.schedule.enabled && !this.config.security.emergencyStop) {
                    this.scheduler.start(this.config);
                }

                this.isInitialized = true;
                this.logger.log('success', 'Telegram助手初始化完成');

                // 显示欢迎通知
                GM_notification({
                    title: 'Telegram助手',
                    text: '助手已启动，点击右下角图标打开配置界面',
                    timeout: 5000
                });

            } catch (error) {
                this.logger.log('error', `初始化失败: ${error.message}`);
                console.error('Telegram助手初始化失败:', error);
            }
        }

        // 等待页面加载
        waitForPageLoad() {
            return new Promise((resolve) => {
                if (document.readyState === 'complete') {
                    resolve();
                    return;
                }

                const checkLoad = () => {
                    if (document.querySelector('.chat-title, .peer-title') ||
                        document.querySelector('#editable-message-text, .input-message-input')) {
                        resolve();
                    } else {
                        setTimeout(checkLoad, 1000);
                    }
                };

                checkLoad();
            });
        }

        // 启动消息监听
        startMessageMonitoring() {
            if (this.monitorInterval) {
                clearInterval(this.monitorInterval);
            }

            this.monitorInterval = setInterval(() => {
                this.checkNewMessages();
            }, 2000); // 每2秒检查一次新消息

            this.logger.log('info', '消息监听已启动');
        }

        // 停止消息监听
        stopMessageMonitoring() {
            if (this.monitorInterval) {
                clearInterval(this.monitorInterval);
                this.monitorInterval = null;
            }
            this.logger.log('info', '消息监听已停止');
        }

        // 检查新消息
        checkNewMessages() {
            try {
                const messages = document.querySelectorAll('.message:not([data-tg-monitored])');

                messages.forEach(messageEl => {
                    messageEl.setAttribute('data-tg-monitored', 'true');

                    const messageText = messageEl.textContent || '';
                    const senderEl = messageEl.querySelector('.sender-name, .peer-title');
                    const sender = senderEl ? senderEl.textContent.trim() : '';

                    if (this.shouldTriggerOnMessage(messageText, sender)) {
                        this.handleTriggeredMessage(messageText, sender);
                    }
                });
            } catch (error) {
                // 静默处理错误，避免干扰正常使用
            }
        }

        // 判断是否应该触发
        shouldTriggerOnMessage(messageText, sender) {
            if (!this.config.monitoring.enabled) return false;

            // 检查用户监听
            if (this.config.monitoring.users.length > 0) {
                const userMatch = this.config.monitoring.users.some(user =>
                    sender.toLowerCase().includes(user.toLowerCase())
                );
                if (!userMatch) return false;
            }

            // 检查关键词
            if (this.config.monitoring.keywords.length === 0) return false;

            const text = this.config.monitoring.caseSensitive ? messageText : messageText.toLowerCase();

            return this.config.monitoring.keywords.some(keyword => {
                const searchKeyword = this.config.monitoring.caseSensitive ? keyword : keyword.toLowerCase();

                switch (this.config.monitoring.matchMode) {
                    case 'exact':
                        return text === searchKeyword;
                    case 'contains':
                        return text.includes(searchKeyword);
                    case 'regex':
                        try {
                            const regex = new RegExp(searchKeyword, this.config.monitoring.caseSensitive ? '' : 'i');
                            return regex.test(text);
                        } catch (e) {
                            return false;
                        }
                    default:
                        return false;
                }
            });
        }

        // 处理触发的消息
        async handleTriggeredMessage(messageText, sender) {
            this.logger.log('info', `检测到触发消息`, { sender, message: messageText });

            // 这里可以添加自动回复逻辑
            // 例如：await this.operator.sendMessage('自动回复消息');

            GM_notification({
                title: '消息触发',
                text: `来自 ${sender}: ${messageText.substring(0, 50)}...`,
                timeout: 3000
            });
        }

        // 公开方法供UI调用
        removeGroup(index) { this.ui.removeGroup(index); }
        removeTemplate(index) { this.ui.removeTemplate(index); }
        removeSpecificTime(index) { this.ui.removeSpecificTime(index); }
        removeKeyword(index) { this.ui.removeKeyword(index); }
        removeMonitorUser(index) { this.ui.removeMonitorUser(index); }
    }

    // 全局变量和初始化
    let telegramApp;

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initApp);
    } else {
        initApp();
    }

    function initApp() {
        // 确保在Telegram页面
        if (!window.location.href.includes('web.telegram.org')) {
            console.log('Telegram助手: 不在Telegram页面，跳过初始化');
            return;
        }

        telegramApp = new TelegramApp();

        // 延迟初始化，等待Telegram界面完全加载
        setTimeout(() => {
            telegramApp.init();
        }, 3000);

        // 将应用实例暴露到全局，供UI事件调用
        window.telegramApp = telegramApp;
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
        if (telegramApp) {
            telegramApp.stopMessageMonitoring();
            if (telegramApp.scheduler) {
                telegramApp.scheduler.stop();
            }
        }
    });

})();