<?php

namespace App\Admin\Controllers\Product;

use App\Models\PublicModels\AchievementModel;
use App\Models\PublicModels\PricingFactorValueModel;
use App\Models\PublicModels\ProductCategoryModel;
use App\Models\PublicModels\ProductLevelModel;
use App\Models\PublicModels\ProductSpecificationModel;
use App\Models\PublicModels\SubjectModel;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Form\Tools;
use Encore\Admin\Grid;
use Encore\Admin\Widgets\Table;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * @method getPricingInfo(int $REGION)
 */
class SpecificationController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '产品规格';

    protected function grid(): Grid
    {
        $rawList = PricingFactorValueModel::all()->toArray();
        $regionList = [];
        $valueList = [];
        foreach ($rawList as $raw) {
            $valueList[$raw['id']] = $raw['name_cn'] . " " . $raw['name_en'];
            if ($raw['pricing_factor_id'] == PricingFactorValueModel::REGION) {
                $regionList[$raw['id']] = $raw['name_cn'];
            } else if ($raw['pricing_factor_id'] == PricingFactorValueModel::PRICING_TYPE) {
                $valueList[$raw['id']] = $raw['name_cn'];
            }
        }

        $grid = new Grid(new ProductSpecificationModel());
        $grid->paginate(15);
        $grid->model()->where('id', '!=', 0)->orderBy('status', 'desc')->orderBy('id', 'desc');

        $grid->column('id', 'ID')->style("text-align: center;")->sortable();
        $grid->column('code', '规格编号')->style("text-align: center;");
        $grid->column('name_cn', '规格中文名')->style("text-align: center;")->editable();
        $grid->column('name_en', '规格英文名')->style("text-align: center;")->editable();
        $grid->column('category.name_cn', '类别中文名')->style("text-align: center;");
        $grid->column('category.type', '产品类型')->using(ProductCategoryModel::TYPE_DESCRIPTION_CN)
            ->modal('产品特有属性', function ($model) use ($valueList) {
                $type = $model->category->type;
                switch ($type) {
                    case ProductCategoryModel::TYPE_COURSE:
                        if ($model->price_type == 0) {
                            $params = [
                                ['价格模式', '单价模式'],
                                [PricingFactorValueModel::factorDescriptionCn[PricingFactorValueModel::CLASS_TYPE], $valueList[$model->classType->value_id]],
                                [PricingFactorValueModel::factorDescriptionCn[PricingFactorValueModel::TEACHER_TYPE], $valueList[$model->teacherType->value_id]],
                                [PricingFactorValueModel::factorDescriptionCn[PricingFactorValueModel::PRICING_TYPE], $valueList[$model->pricingType->value_id]],
                            ];
                        } else {
                            $params = [
                                ['价格模式', '总价模式'],
                                ['总价模式总课量', $model->static_quantity],
                                [PricingFactorValueModel::factorDescriptionCn[PricingFactorValueModel::CLASS_TYPE], $valueList[$model->classType->value_id]],
                                [PricingFactorValueModel::factorDescriptionCn[PricingFactorValueModel::TEACHER_TYPE], $valueList[$model->teacherType->value_id]],
                                [PricingFactorValueModel::factorDescriptionCn[PricingFactorValueModel::PRICING_TYPE], $valueList[$model->pricingType->value_id]],
                            ];
                        }
                        return new Table(['属性', '值'], $params, ['class' => 'table table-bordered table-striped text-center',]);
                    case ProductCategoryModel::TYPE_PLAN:
                        //todo 返回计划配置
                        return '计划类展示开发中';
                    case 3:
                    case 4:
                    case 5:
                    default:
                        return "无";
                }
            })->style("text-align: center;");
        $grid->column('custom_price', '是否支持自定义价格')->using([0 => '否', 1 => '是'])->style("text-align: center;");
        $grid->column('level.name_cn', '业务分级')->style("text-align: center;");
        $grid->column('achievement.name', '绩效类型')->style("text-align: center;");
        $grid->column('currency', '货币')->using([1 => '$', 2 => '¥'])->style("text-align: center;");
        $grid->column('currentPrice.price', '当前价格')->display(function ($price) {
            return $price ?? '未设置';
        })->modal('价格', function ($model) {
            $prices = $model->prices()->get()->map(function ($price) {
                $price = $price->only(['price', 'start_date', 'end_date', 'status']);
                $price['status'] = $price['status'] == 1 ? '启用' : '禁用';
                return $price;
            });
            return new Table(['价格', '开始时间', '结束时间', '状态'], $prices->toArray(), ['class' => 'table table-bordered table-striped text-center',]);
        })->style("text-align: center;");
        $grid->column('科目')->display(function () {
            return '查看科目';
        })->modal('科目', function ($model) {
            $subjects = $model->subjects()->get()->map(function ($subject) {
                $thisSubject = $subject->subject()->first();
                return [
                    'subject.name_cn' => $thisSubject->name_cn,
                    'subject.name_en' => $thisSubject->name_en,
                    'quantity' => $subject->quantity,
                    'nct_quantity' => $subject->nct_quantity,];
            });
            return new Table(['科目中文名', '科目英文名', '数量', 'NCT数量'], $subjects->toArray(), ['class' => 'table table-bordered table-striped text-center',]);
        })->style("text-align: center;");
        $grid->column('region.value_id', '地区')->style("text-align: center;")->using($regionList);

        $grid->column('status', '状态')->using([1 => '启用', 0 => '禁用'])->filter([0 => '禁用', 1 => '启用'])->label([1 => 'success', 0 => 'danger'])->style("text-align: center;");
        $grid->column('memo', '备注');
//        $grid->column('created_at', '创建时间')->style("text-align: center;");
//        $grid->column('updated_at', '更新时间')->style("text-align: center;");

        $grid->filter(function ($filter) use ($regionList) {
            // 去掉默认的id过滤器
            $filter->disableIdFilter();
            $filter->like('name_cn', '规格中文名');
            $filter->like('name_en', '规格英文名');
            $filter->in('category_id', '产品类别')->multipleSelect(ProductCategoryModel::all()->pluck('name_cn', 'id'));
            $filter->in('category.type', '产品类型')->multipleSelect(ProductCategoryModel::TYPE_DESCRIPTION_CN);
            $filter->in('currency', '货币')->select([1 => '美元', 2 => '人民币']);
            $filter->in('level_id', '业务分级')->multipleSelect(ProductLevelModel::all()->pluck('name_cn', 'id'));
            $filter->in('achievement_id', '绩效类型')->multipleSelect(AchievementModel::all()->pluck('name', 'id'));
            $filter->in('region.value_id', '地区')->multipleSelect($regionList);
        });

        //actions 相关
        $grid->actions(function ($actions) {
            // 去掉删除
            $actions->disableDelete();
            // 去掉查看
            $actions->disableView();
        });

        //禁用导出
        $grid->disableExport();
        //禁用多选
        $grid->disableRowSelector();

        return $grid;
    }


    protected function form(): Form
    {
        $form = new Form(new ProductSpecificationModel);

        if ($form->isCreating()) {
            $categories = ProductCategoryModel::where('status', 1)->whereNotIn('type', [ProductCategoryModel::TYPE_PLAN, ProductCategoryModel::TYPE_OTHER])->get()->toArray();
        } else {
            $categories = ProductCategoryModel::all()->toArray();
        }
        $categoryList = [];
        $classTypeCategoryList = [];
        foreach ($categories as $category) {
            $categoryList[$category['id']] = $category['name_cn'];
            if ($category['type'] == ProductCategoryModel::TYPE_COURSE) {
                $classTypeCategoryList[] = $category['id'];
            }
        }
        $rawList = PricingFactorValueModel::all()->toArray();
        $valueList = [];
        $classTypeList = [];
        $teacherTypeList = [];
        $pricingTypeList = [];
        foreach ($rawList as $raw) {
            $valueList[$raw['id']] = $raw['name_cn'] . " " . $raw['name_en'];
            if ($raw['pricing_factor_id'] == PricingFactorValueModel::PRICING_TYPE) {
                $valueList[$raw['id']] = $raw['name_cn'];
                $pricingTypeList[$raw['id']] = $raw['name_cn'];
            } elseif ($raw['pricing_factor_id'] == PricingFactorValueModel::CLASS_TYPE) {
                $classTypeList[$raw['id']] = $raw['name_cn'];
            } elseif ($raw['pricing_factor_id'] == PricingFactorValueModel::TEACHER_TYPE) {
                $teacherTypeList[$raw['id']] = $raw['name_cn'];
            }
        }
        //设置更新人和创建人为当前登录人
        $form->hidden('updated_by')->value(auth()->user()->id);
        $form->hidden('created_by')->default(auth()->user()->id);

        $form->column(1 / 2, function ($form) use ($valueList) {
            $form->text('code', '规格编码')->default('SKU')->readonly();
            $form->text('name_cn', '规格中文名')->default('自动生成规格名')->help("使用'自动生成规格名'，系统将参照规则自动生成规格名称");
            $form->text('name_en', '规格英文名');

            if ($form->isCreating()) {
                $form->radio('currency', '货币')->options([2 => '人民币', 1 => '美元'])->default(2);
                $form->switch('custom_price', '是否自定义价格')->states(['on' => ['value' => 1, 'text' => '是', 'color' => 'success'], 'off' => ['value' => 0, 'text' => '否', 'color' => 'danger']])->default(0);
                $form->select('achievement_id', '绩效类型')->options(AchievementModel::all()->pluck('name', 'id'))->required();
            } else {
                $form->select('currency', '货币')->options([1 => '美元', 2 => '人民币'])->readonly();
                $form->switch('custom_price', '是否自定义价格')->states(['on' => ['value' => 1, 'text' => '是', 'color' => 'success'], 'off' => ['value' => 0, 'text' => '否', 'color' => 'danger']])->readonly();
                $form->select('achievement_id', '绩效类型')->options(AchievementModel::all()->pluck('name', 'id'))->readonly();
            }
            $form->select('region.value_id', '地区')->options($valueList)->default(6)->readonly();
            $form->hidden('region.pricing_factor_id')->value(PricingFactorValueModel::REGION);

            $form->switch('status', '状态')->states(['on' => ['value' => 1, 'text' => '启用', 'color' => 'success'], 'off' => ['value' => 0, 'text' => '禁用', 'color' => 'danger']])->default(1);
            $form->text('memo', '备注');
            $form->hasMany('prices', "价格信息", function (Form\NestedForm $form) {
                $form->decimal('price', '价格', 2)->required();
                $form->dateRange('start_date', 'end_date', '起止日期')->required();
                $form->switch('status', '状态')->states(['on' => ['value' => 1, 'text' => '启用', 'color' => 'success'], 'off' => ['value' => 0, 'text' => '禁用', 'color' => 'danger']])->default(1);
                $form->hidden('created_by')->default(auth()->user()->id);
                $form->hidden('created_at')->default(date('Y-m-d H:i:s'));

            });
        });

        $form->column(1 / 2, function ($form) use ($categoryList, $classTypeCategoryList, $valueList, $classTypeList, $teacherTypeList, $pricingTypeList) {
            if ($form->isCreating()) {
                $form->select('category_id', '产品类别')->options($categoryList)->when('in', $classTypeCategoryList, function (Form $form) use ($classTypeList, $teacherTypeList, $pricingTypeList) {
                    $form->radio('price_type', '价格模式')->options([0 => '单价模式', 1 => '总价模式'])->default(0)->when(1, function (Form $form) {
                        $form->number('static_quantity', '总价模式总课量')->default(0);
                    });
                    $form->select('classType.value_id', '班型')->options($classTypeList);
                    $form->hidden('classType.pricing_factor_id')->value(PricingFactorValueModel::CLASS_TYPE);
                    $form->select('teacherType.value_id', '教师类型')->options($teacherTypeList);
                    $form->hidden('teacherType.pricing_factor_id')->value(PricingFactorValueModel::TEACHER_TYPE);
                    $form->select('pricingType.value_id', '定价方案')->options($pricingTypeList);
                    $form->hidden('pricingType.pricing_factor_id')->value(PricingFactorValueModel::PRICING_TYPE);
                    $this->getSubjects($form);
                })->load('level_id', '/admin/crm_product/specification_level')->required();
                $form->select('level_id', '业务分级')->required();
            } else {
                $form->select('category_id', '产品类别')->options($categoryList)->readonly()->when('in', $classTypeCategoryList, function (Form $form) use ($valueList) {
                    $form->select('price_type', '价格模式')->options([0 => '单价模式', 1 => '总价模式'])->when(1, function (Form $form) {
                        $form->number('static_quantity', '总价模式总课量')->default(0);
                    })->readonly();
                    $form->select('classType.value_id', '班型')->options($valueList)->readonly();
                    $form->select('teacherType.value_id', '教师类型')->options($valueList)->readonly();
                    $form->select('pricingType.value_id', '定价方案')->options($valueList)->readonly();
                    $this->getSubjects($form);
                })->readonly();
                $form->select('level_id', '业务分级')->options(ProductLevelModel::all()->pluck('name_cn', 'id'))->readonly();
            }
        });

        //保存后回调
        $form->saved(function (Form $form) use ($valueList, $classTypeCategoryList) {
            $id = $form->model()->id;
            $spec = ProductSpecificationModel::find($id);
            if ($form->isCreating()) {
                //生成编号
                $spec->code = 'SKU' . $id;
            }
            if ($spec->name_cn == '自动生成规格名') {
                //todo 生成中文规格名
                $level = ProductLevelModel::find($spec->level_id);
                if (in_array($spec->category_id, $classTypeCategoryList)) {
                    $classType = $spec->classType->value_id;
                    $teacherType = $spec->teacherType->value_id;
                    $specNameCn = $valueList[$teacherType] . '·' . $spec->category->name_cn . '·' . $level->name_cn . '·' . $valueList[$classType];
                } else {
                    $specNameCn = $spec->category->name_cn . '·' . $level->name_cn;
                }
                $spec->name_cn = $specNameCn;
            }
            $spec->save();
        });

        //表单工具控制
        $form->tools(function (Tools $tools) {
            // 去掉`删除`按钮
            $tools->disableDelete();
            // 去掉`查看`按钮
            $tools->disableView();
        });

        $form->footer(function ($footer) {
            // 去掉`查看`checkbox
            $footer->disableViewCheck();
        });

        return $form;
    }


    /**
     * 获取业务分级
     * @param Request $request
     * @return mixed
     */
    public function level(Request $request): mixed
    {
        $category_id = $request->get('q');
        return ProductLevelModel::where('category_id', $category_id)->where('status', 1)->get(['id', DB::raw('name_cn as text')]);
    }

    /**
     * @param Form $form
     * @return void
     */
    protected function getSubjects(Form $form): void
    {
        $form->hasMany('subjects', "科目信息", function (Form\NestedForm $form) {
            $form->select('subject_id', '科目')->options(SubjectModel::all()->pluck('name_cn', 'id'));
            $form->number('quantity', '数量')->default(1);
            $form->number('nct_quantity', 'NCT数量')->default(0);
        });
    }
}
