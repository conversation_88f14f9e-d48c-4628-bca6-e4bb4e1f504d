<?php

namespace App\Admin\Controllers\Product;

use App\Models\PublicModels\BuModel;
use App\Models\PublicModels\ProductCategoryFactorModel;
use App\Models\PublicModels\ProductCategoryModel;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Form\Tools;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Encore\Admin\Widgets\Table;

class CategoryController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '产品类别';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        $grid = new Grid(new ProductCategoryModel);
        $grid->paginate(50);
        $grid->model()->where('id', '!=', 0);

        $grid->column('id', '产品类别ID')->modal('产品业务分级', function ($model) {
            $level = $model->levels()->get()->map(function ($level) {
                $level = $level->only(['id', 'name_cn', 'name_en', 'status']);
                $level['status'] = $level['status'] == 1 ? '启用' : '禁用';
                return $level;
            });
            return new Table(['ID', '分级中文名', '分级英文名', '状态'], $level->toArray());
        })->sortable();
        $grid->column('name_cn', '产品类别中文名');
        $grid->column('name_en', '产品类别英文名');
        $grid->column('type', '类型')->replace(ProductCategoryModel::TYPE_DESCRIPTION_CN)->filter(ProductCategoryModel::TYPE_DESCRIPTION_CN)->style("text-align: center;");
        $grid->column('value_chain_id', '价值链')->replace([1 => 'A', 2 => 'B', 3 => 'C', 4 => 'Other'])->filter([1 => 'A', 2 => 'B', 3 => 'C', 4 => 'Other'])->style("text-align: center;")->sortable();
        $grid->column('bu.name', '产品线')->filter(BuModel::all()->pluck('name', 'id')->toArray())->style("text-align: center;");
        $grid->column('');
        $grid->column('memo', '备注');
        $grid->column('status', '状态')->replace([1 => '启用', 0 => '禁用'])->filter([1 => '启用', 0 => '禁用'])->label([1 => 'success', 0 => 'danger'])->style("text-align: center;");

        $grid->filter(function ($filter) {
            // 去掉默认的id过滤器
            $filter->disableIdFilter();
            $filter->like('name_cn', '产品类别中文名');
            $filter->like('name_en', '产品类别英文名');
            $filter->like('memo', '备注');

        });

        //actions 相关
        $grid->actions(function ($actions) {
            // 去掉删除
            $actions->disableDelete();
            // 去掉编辑
            // $actions->disableEdit();
            // 去掉查看
//            $actions->disableView();
        });

        //禁用导出
        $grid->disableExport();
        //禁用多选
        $grid->disableRowSelector();


        return $grid;
    }


    protected function form(): Form
    {
        $form = new Form(new ProductCategoryModel);
        //设置更新人和创建人为当前登录人
        $form->hidden('updated_by')->value(auth()->user()->id);
        $form->hidden('created_by')->default(auth()->user()->id);

        $form->column(1 / 2, function ($form) {
            //禁用部分类型
            $removeTypes = [ProductCategoryModel::TYPE_OTHER => '', ProductCategoryModel::TYPE_PLAN => ''];
            $form->text('name_cn', '产品类别中文名')->required();
            $form->text('name_en', '产品类别英文名');
            if ($form->isCreating()) {
                $form->select('type', '类型')->options(array_diff_key(ProductCategoryModel::TYPE_DESCRIPTION_CN, $removeTypes))->required();
                $form->select('value_chain_id', '价值链')->options([1 => 'A', 2 => 'B', 3 => 'C', 4 => 'Other'])->required();
                $form->select('bu_id', '产品线')->options(BuModel::where('is_deleted', 0)->get()->pluck('name', 'id'))->required();
            } else {
                $form->select('type', '类型')->options(ProductCategoryModel::TYPE_DESCRIPTION_CN)->readonly();
                $form->select('value_chain_id', '价值链')->options([1 => 'A', 2 => 'B', 3 => 'C', 4 => 'Other'])->readonly();
                $form->select('bu_id', '产品线')->options(BuModel::all()->pluck('name', 'id'))->readonly();
            }
            $form->text('memo', '备注');
            $form->switch('status', '状态')->states(['on' => ['value' => 1, 'text' => '启用', 'color' => 'success'], 'off' => ['value' => 0, 'text' => '禁用', 'color' => 'danger']])->default(1);
        });
        $form->column(1 / 2, function ($form) {
            $form->hasMany('levels', "业务分级", function (Form\NestedForm $form) {
                $form->text('name_cn', '分级中文名')->required();
                $form->text('name_en', '分级英文名');
                $form->switch('status', '状态')->states(['on' => ['value' => 1, 'text' => '启用', 'color' => 'success'], 'off' => ['value' => 0, 'text' => '禁用', 'color' => 'danger']])->default(1);
            });
        });

        $form->tools(function (Tools $tools) {
            // 去掉`删除`按钮
            $tools->disableDelete();
            // 去掉`查看`按钮
//            $tools->disableView();
        });

        $form->footer(function ($footer) {
            // 去掉`查看`checkbox
            $footer->disableViewCheck();
            // 去掉`继续编辑`checkbox
//            $footer->disableEditingCheck();
        });

        //保存后回调
        $form->saved(function (Form $form) {
            //处理产品类别定价因素
            $category_id = $form->model()->id;
            if ($form->isCreating()) {
                $type = $form->model()->type;
                if ($type == 1) {
                    ProductCategoryFactorModel::insert([
                        ['category_id' => $category_id, 'pricing_factor_id' => 1,],
                        ['category_id' => $category_id, 'pricing_factor_id' => 2,],
                        ['category_id' => $category_id, 'pricing_factor_id' => 3,],
                        ['category_id' => $category_id, 'pricing_factor_id' => 4,],
                    ]);
                } else {
                    ProductCategoryFactorModel::insert(['category_id' => $category_id, 'pricing_factor_id' => 1,]);
                }
            }
        });

        return $form;
    }


    public function detail($id): Show
    {
        $show = new Show(ProductCategoryModel::findOrFail($id));
        $show->field('id', 'ID');
        $show->field('name_cn', '产品类别中文名');
        $show->field('name_en', '产品类别英文名');
        $show->field('type', '类型')->using(ProductCategoryModel::TYPE_DESCRIPTION_CN);
        $show->field('value_chain_id', '价值链')->using([1 => 'A', 2 => 'B', 3 => 'C', 4 => 'Other']);
        $show->field('bu_id', '产品线')->using(BuModel::all()->pluck('name', 'id')->toArray());
        $show->field('memo', '备注');
        $show->field('status', '状态')->using([0 => '禁用', 1 => '启用']);
        $show->field('created_at', '创建时间');
        $show->field('updated_at', '更新时间');
        $show->divider();
        $show->field('业务分级')->as(function () use ($id) {
            $levels = ProductCategoryModel::find($id)->levels()->get()->map(function ($level) {
                $level = $level->only(['name_cn', 'status']);
                $level['status'] = $level['status'] == 1 ? '启用' : '禁用';
                return $level;
            })->toArray();
            $res = [];
            foreach ($levels as $level) {
                $res[] = $level['name_cn'] . ' (' . $level['status'] . ')';
            }
            return $res;
        })->label();

        $show->panel()->tools(function ($tools) {
            $tools->disableDelete();
        });

        return $show;
    }
}
