<?php

namespace App\Admin\Controllers\Admin;

use App\Models\DepartmentModels\DeptModel;
use App\Models\DepartmentModels\SharePrivilegeModel;
use App\Models\DepartmentModels\UserModel;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Form\Tools;
use Encore\Admin\Grid;

class SharePrivilegeController extends AdminController
{

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Crm Share Privilege';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        $userList = [];
        $userListTag = [];
        foreach (UserModel::all() as $user) {
            $userList[$user->id] = $user->name . '(' . $user->user_code . ')' . ($user->user_status == UserModel::USER_STATUS_ONLINE ? '' : '(离职)');
            $userListTag[$user->id] = $user->user_status == UserModel::USER_STATUS_ONLINE ? 'success' : 'danger';
        }
        $deptList = [];
        $deptListTag = [];
        foreach (DeptModel::all() as $dept) {
            $deptList[$dept->id] = $dept->dept_name . ($dept->is_deleted == DeptModel::DEPT_IS_DELETED ? '(已删除)' : '');
            $deptListTag[$dept->id] = $dept->is_deleted == DeptModel::DEPT_IS_DELETED ? 'danger' : 'success';
        }
        $roleList = SharePrivilegeModel::$shareRoleDescriptions;
        $grid = new Grid(new SharePrivilegeModel);
        $grid->paginate(50);
        $grid->model()->where('id', '!=', 0);

        $grid->column('share_type', '权限类型')->replace(SharePrivilegeModel::$shareTypeDescriptions)->filter(SharePrivilegeModel::$shareTypeDescriptions)->label()->style("text-align: center;");
        $grid->column('obj_type', '对象类型')->replace(SharePrivilegeModel::$objTypeDescriptions)->label()->style("text-align: center;");
        $grid->column('roles', '权限角色')->display(function ($roles) use ($roleList) {
            $result = [];
            foreach ($roles as $role) $result[] = "<span class='label label-warning'>{$roleList[$role]}</span>";
            return join('&nbsp;', $result);
        })->style("text-align: center;");
        $grid->column('from_ids', '数据源')->display(function ($from_ids) use ($userList, $deptList, $userListTag, $deptListTag) {
            $result = [];
            if (in_array($this->obj_type,
                [
                    SharePrivilegeModel::OBJ_TYPE_PERSON_TO_PERSON,
                    SharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON,
                    SharePrivilegeModel::OBJ_TYPE_PERSON_TO_DEPARTMENT,
                    SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_TO_DEPARTMENT,
                ]
            )) {
                $targetArray = $userList;
                $targetArrayTag = $userListTag;
            } else {
                $targetArray = $deptList;
                $targetArrayTag = $deptListTag;
            }
            foreach ($from_ids as $from_id) $result[] = "<span class='label label-{$targetArrayTag[$from_id]}'>{$targetArray[$from_id]}</span>";
            return join('&nbsp;', $result);
        })->style("text-align: center;width: 300px");
        $grid->column('to_ids', '对象')->display(function ($to_ids) use ($userList, $deptList, $userListTag, $deptListTag) {
            $result = [];
            if (in_array($this->obj_type,
                [
                    SharePrivilegeModel::OBJ_TYPE_PERSON_TO_PERSON,
                    SharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON,
                    SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_TO_PERSON,
                    SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_PERSON,
                ]
            )) {
                $targetArray = $userList;
                $targetArrayTag = $userListTag;
            } else {
                $targetArray = $deptList;
                $targetArrayTag = $deptListTag;
            }
            foreach ($to_ids as $to_id) $result[] = "<span class='label label-{$targetArrayTag[$to_id]}'>{$targetArray[$to_id]}</span>";
            return join('&nbsp;', $result);
        })->style("text-align: center;width: 300px");

        $grid->column('status', '状态')->replace(SharePrivilegeModel::$statusDescriptions)->filter(SharePrivilegeModel::$statusDescriptions)->label()->style("text-align: center;");
        $grid->column('start_date', '开始时间')->sortable()->style("text-align: center;");
        $grid->column('end_date', '结束时间')->sortable()->style("text-align: center;");
        $grid->column('memo', '备注')->width(200);
        $grid->column('created_by', '创建人')->replace($userList)->style("text-align: center;");
        $grid->column('updated_by', '更新人')->replace($userList)->style("text-align: center;");
        $grid->column('created_at', '创建时间')->sortable()->style("text-align: center;");
        $grid->column('updated_at', '更新时间')->sortable()->style("text-align: center;");


        $grid->filter(function ($filter) use ($userList, $deptList) {
            // 去掉默认的id过滤器
            $filter->disableIdFilter();
            // 在这里添加字段过滤器
            $filter->in('obj_type', '对象类型')->multipleSelect(SharePrivilegeModel::$objTypeDescriptions);
            $filter->date('start_date', '开始时间');
            $filter->date('end_date', '结束时间');
            $filter->in('status', '状态')->multipleSelect(SharePrivilegeModel::$statusDescriptions);
            $filter->like('memo', '备注');
            $filter->in('created_by', '创建人')->multipleSelect($userList);
            $filter->in('updated_by', '更新人')->multipleSelect($userList);

        });

        //actions 相关
        $grid->actions(function ($actions) {

            // 去掉删除
            $actions->disableDelete();


            // 去掉编辑
            // $actions->disableEdit();
            // 去掉查看
            $actions->disableView();
        });

        //禁用导出
        $grid->disableExport();
        //禁用多选
        $grid->disableRowSelector();


        return $grid;
    }


    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        $form = new Form(new SharePrivilegeModel);
        $userList = [];
        foreach (UserModel::all()->sortByDesc(['user_status']) as $user) $userList[$user->id] = $user->name . '(' . $user->user_code . ')' . ($user->user_status == 1 ? '' : '(离职)');
        $deptList = [];
        foreach (DeptModel::all()->sortBy(['is_deleted']) as $dept) $deptList[$dept->id] = $dept->dept_name . ($dept->is_deleted == 1 ? '(已删除)' : '');

        //设置更新人和创建人为当前登录人
        $form->hidden('updated_by')->value(auth()->user()->id);
        $form->hidden('created_by')->default(auth()->user()->id);

        $form->display('id', __('ID'));
        $form->radio('share_type', '权限类型')->options(SharePrivilegeModel::$shareTypeDescriptions)->default(0)->required()->when(SharePrivilegeModel::SHARE_TYPE_EXTRA, function (Form $form) {
            $form->multipleSelect('roles', '权限角色')->options(SharePrivilegeModel::$shareRoleDescriptions);
        });
        $form->radio('obj_type', '对象类型')->options(SharePrivilegeModel::$objTypeDescriptions)
            ->when(SharePrivilegeModel::OBJ_TYPE_PERSON_TO_PERSON, function (Form $form) use ($userList) {
                $form->listbox('from_ids', '数据源')->options($userList);
                $form->listbox('to_ids', '对象')->options($userList);
            })
            ->when(SharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON, function (Form $form) use ($userList) {
                $form->listbox('from_ids', '数据源')->options($userList);
                $form->listbox('to_ids', '对象')->options($userList);
            })
            ->when(SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_TO_PERSON, function (Form $form) use ($userList, $deptList) {
                $form->listbox('from_ids', '数据源')->options($deptList);
                $form->listbox('to_ids', '对象')->options($userList);
            })
            ->when(SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_PERSON, function (Form $form) use ($userList, $deptList) {
                $form->listbox('from_ids', '数据源')->options($deptList);
                $form->listbox('to_ids', '对象')->options($userList);
            })
            ->when(SharePrivilegeModel::OBJ_TYPE_PERSON_TO_DEPARTMENT, function (Form $form) use ($userList, $deptList) {
                $form->listbox('from_ids', '数据源')->options($userList);
                $form->listbox('to_ids', '对象')->options($deptList);
            })
            ->when(SharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT, function (Form $form) use ($userList, $deptList) {
                $form->listbox('from_ids', '数据源')->options($userList);
                $form->listbox('to_ids', '对象')->options($deptList);
            })
            ->when(SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_TO_DEPARTMENT, function (Form $form) use ($deptList) {
                $form->listbox('from_ids', '数据源')->options($deptList);
                $form->listbox('to_ids', '对象')->options($deptList);
            })
            ->when(SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_DEPARTMENT, function (Form $form) use ($deptList) {
                $form->listbox('from_ids', '数据源')->options($deptList);
                $form->listbox('to_ids', '对象')->options($deptList);
            })
            ->default(SharePrivilegeModel::OBJ_TYPE_PERSON_TO_PERSON)
            ->required();
        $form->dateRange('start_date', 'end_date', '有效期')->required();
        $form->switch('status', '状态')->default(1);
        $form->text('memo', '备注');

        $form->tools(function (Tools $tools) {
            // 去掉`删除`按钮
            $tools->disableDelete();
            // 去掉`查看`按钮
            $tools->disableView();
        });

        $form->saving(function (Form $form) {
            if ($form->share_type == SharePrivilegeModel::SHARE_TYPE_SHARE) {
                $form->roles = [0 => null];
            }
        });

        $form->footer(function ($footer) {
            // 去掉`查看`checkbox
            $footer->disableViewCheck();
            // 去掉`继续编辑`checkbox
            $footer->disableEditingCheck();
        });

        return $form;
    }

}
