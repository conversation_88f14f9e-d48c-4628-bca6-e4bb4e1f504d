<?php

namespace App\Admin\Controllers\Admin;

use App\Models\DepartmentModels\PermissionModel;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Encore\Admin\Show;
use Spatie\Permission\Models\Role;

class RolesController extends AdminController
{

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Crm Roles';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        $grid = new Grid(new Role);
        $grid->model()->where('id', '!=', 0);

        $grid->column('id', __('ID'))->sortable();
        $grid->column('use_name', '角色名称');
        $grid->column('name', '角色代号');
        $grid->column('guard_name', 'Guard');
        $grid->column('permissions', '相关权限')->pluck('name')->label();
        $grid->column('created_at', __('创建时间'));
        $grid->column('updated_at', __('更新时间'));
        $grid->filter(function ($filter) {

            // 去掉默认的id过滤器
            $filter->disableIdFilter();
            // 在这里添加字段过滤器
            $filter->like('use_name', '角色名称');
            $filter->like('name', '角色代号');
            $filter->like('guard_name', 'Guard');
        });

        //actions 相关
        $grid->actions(function ($actions) {
            // 去掉删除
            $actions->disableDelete();
            // 去掉查看
            $actions->disableView();
        });

        //禁用导出
        $grid->disableExport();
        //禁用多选
        $grid->disableRowSelector();


        return $grid;
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id): Show
    {
        $show = new Show(Role::findOrFail($id));

        $show->field('id', __('ID'));
        $show->field('name', __('角色名称'));
        $show->field('guard_name', __('Guard'));
        $show->field('created_at', __('创建时间'));
        $show->field('updated_at', __('更新时间'));

        $show->permissions('权限', function ($permission) {
            $permission->resource('/admin/auth/permissions');
            $permission->id();
            $permission->name('权限名称');
            $permission->slug('Slug');
        });


        return $show;
    }


    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        $form = new Form(new Role);
        $form->display('id', __('ID'));
        $form->text('use_name', '角色名称')->required();
        $form->text('name', '角色代号')->required();
        $form->text('guard_name', 'Guard')->required();
        $form->multipleSelect('permissions', __('权限'))->options(PermissionModel::all()->pluck('name', 'id'));
        $form->tools(function (Form\Tools $tools) {
            // 去掉`删除`按钮
            $tools->disableDelete();
            // 去掉`查看`按钮
            $tools->disableView();
        });
        // 去掉`查看`checkbox
        $form->disableViewCheck();
        return $form;
    }

}
