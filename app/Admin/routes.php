<?php

use Illuminate\Routing\Router;

Admin::routes();

Route::group([
    'prefix'        => config('admin.route.prefix'),
    'namespace'     => config('admin.route.namespace'),
    'middleware'    => config('admin.route.middleware'),
    'as'            => config('admin.route.prefix') . '.',
], function (Router $router) {

    $router->get('/', 'HomeController@index')->name('home');

    $router->group(['prefix' => 'crm_admin', 'namespace' => 'Admin'], function ($router) {
        $router->resource('users', 'UsersController');
        $router->resource('roles', 'RolesController');
        $router->resource('permissions', 'PermissionController');
        $router->resource('dept', 'DepartController');
        $router->resource('share_privilege', 'SharePrivilegeController');
    });

    $router->group(['prefix' => 'crm_product', 'namespace' => 'Product'], function ($router) {
        $router->resource('category', 'CategoryController');
        $router->resource('subject', 'SubjectController');
        $router->resource('specification', 'SpecificationController');

        //产品规格创建页面联动获取选中产品类别的业务分级
        $router->get('specification_level', 'SpecificationController@level');
    });
});
