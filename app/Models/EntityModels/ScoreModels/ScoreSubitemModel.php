<?php

namespace App\Models\EntityModels\ScoreModels;

use App\Models\PublicModels\SubjectModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class ScoreSubitemModel extends Model
{
    use SoftDeletes;

    protected $table = 'score_subitem';

    public function subject(): HasOne
    {
        return $this->hasOne(SubjectModel::class, 'id', 'subject_id');
    }
}
