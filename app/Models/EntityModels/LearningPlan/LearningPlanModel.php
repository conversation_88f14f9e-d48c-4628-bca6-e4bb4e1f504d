<?php

namespace App\Models\EntityModels\LearningPlan;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LearningPlanModel extends Model
{
    use SoftDeletes;

    protected $table = 'learning_plan';

    public function detail()
    {
        return $this->hasMany(LearningPlanDetailModel::class, 'learning_plan_id', 'id');
    }

    public function score()
    {
        return $this->hasMany(LearningPlanScoreModel::class, 'learning_plan_id', 'id');
    }
}
