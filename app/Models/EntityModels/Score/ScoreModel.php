<?php

namespace App\Models\EntityModels\Score;

use App\Models\EntityModels\EntityModel;
use App\Models\PublicModels\UploadFileModel;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * 成绩模型
 *
 * @property int $id 主键ID
 * @property int $customer_id 学员ID
 * @property int $template_id 规格ID
 * @property int $created_by 创建人ID
 * @property int|null $exam_type 考试类型:1真考 2模考 3其他
 * @property int|null $score_status 出分状态:1正常出分 2未参加考试 3取消成绩 4延迟出分 5不愿告知
 * @property int|null $have_writing 是否考写作
 * @property int|null $is_xkt 是否新课堂出分
 * @property string $target 目标成绩
 * @property string $total 实际成绩
 * @property string $memo 备注
 * @property string|null $exam_date 考试时间
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @property Carbon|null $deleted_at 删除时间
 * @property-read ScoreTemplateModel $template 关联的成绩模板
 * @property-read Collection<ScoreSubitemModel> $subject 关联的成绩科目
 * @property-read Collection<UploadFileModel> $file 关联的上传文件
 */
class ScoreModel extends Model implements EntityModel
{
    use SoftDeletes;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'score';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_id',
        'template_id',
        'created_by',
        'exam_type',
        'score_status',
        'have_writing',
        'is_xkt',
        'target',
        'total',
        'memo',
        'exam_date',
    ];

    /**
     * 日期字段
     *
     * @var array<string, string>
     */
    protected $dates = [
        'exam_date',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 关联成绩模板
     *
     * @return HasOne
     */
    public function template(): HasOne
    {
        return $this->hasOne(ScoreTemplateModel::class, 'id', 'template_id');
    }

    /**
     * 关联成绩科目
     *
     * @return HasMany
     */
    public function subitem(): HasMany
    {
        return $this->hasMany(ScoreSubitemModel::class, 'score_id', 'id');
    }

    /**
     * 关联上传文件
     *
     * @return HasMany
     */
    public function file(): HasMany
    {
        return $this->hasMany(UploadFileModel::class, 'resource_fk', 'id')
            ->where('resource_type', '=', UploadFileModel::RESOURCE_TYPE_SCORE);
    }

    /**
     * 获取实体的负责人ID
     *
     * @return int 创建人ID
     */
    public function getOwnerId(): int
    {
        return $this->created_by;
    }

    public function subject()
    {
        return $this->hasManyThrough(CrmScoreSubjectModel::class, CrmScoreSubitemModel::class, 'score_id', 'id', 'id','subject_id');
    }
}
