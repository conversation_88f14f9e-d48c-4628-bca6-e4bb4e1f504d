<?php

namespace App\Models\DepartmentModels;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * Class DeptModel
 *
 * 部门模型
 *
 * @property int $id 主键ID
 * @property string $dept_id 企业微信部门id
 * @property string $dept_name 部门名称
 * @property int|null $parent_id 企业微信上级部门id
 * @property string|null $dept_num 部门编号（用于快速查询下级）
 * @property string|null $oa_dept_id OA系统部门ID
 * @property string|null $ehr_id EHR系统ID
 * @property string|null $ehr_uid EHR系统负责人ID
 * @property string|null $manage_user 负责人工号
 * @property int $is_deleted 是否删除：0-否，1-是
 * @property int $depth 组织树深度
 * @property int|null $crm_mpg_id 对应原有crm的mpgid
 * @property int $is_mpg 是否MPG：0-否，1-是
 * @property int $manage_id 部门负责人ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @property Carbon|null $deleted_at 删除时间
 *
 * @property-read UserModel $owner 部门负责人
 * @property-read Collection|UserModel[] $members 部门成员
 * @property-read Collection|UserModel[] $activeMembers 在职部门成员
 * @property-read DeptModel|null $parent 父部门
 * @property-read Collection|DeptModel[] $children 子部门
 */
class DeptModel extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'dept';

    /**
     * 主键名
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 是否自增
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'dept_id',
        'dept_name',
        'parent_id',
        'dept_num',
        'oa_dept_id',
        'ehr_id',
        'ehr_uid',
        'manage_user',
        'is_deleted',
        'depth',
        'crm_mpg_id',
        'is_mpg',
        'manage_id',
    ];

    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'is_deleted' => 'boolean',
        'is_mpg' => 'boolean',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'deleted_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 隐藏的字段
     *
     * @var array
     */
    protected $hidden = [
        // 敏感字段可以在这里隐藏
    ];

    /**
     * 已删除状态
     */
    public const int DEPT_IS_DELETED = 1;

    /**
     * Get the owner of the department.
     * 获取部门负责人
     *
     * @return BelongsTo
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(UserModel::class, 'manage_id', 'id');
    }


    /**
     * Get the members of the department.
     * 获取部门成员
     *
     * @return HasMany
     */
    public function menbers(): HasMany
    {
        return $this->hasMany(UserModel::class, 'crm_depart_id', 'id');
    }

    /**
     * Get the active members of the department.
     * 获取在职部门成员
     *
     * @return HasMany
     */
    public function activeMenbers(): HasMany
    {
        return $this->hasMany(UserModel::class, 'crm_depart_id', 'id')->where('user_status', '1');
    }


    /**
     * Get the children of the department.
     * 获取子部门
     * @return mixed
     */
    public function children(): mixed
    {
        return self::where('dept_num', 'like', $this->dept_num . '%')->where('is_deleted', '0')->get();
    }


}
