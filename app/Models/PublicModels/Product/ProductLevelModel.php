<?php

namespace App\Models\PublicModels\Product;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductLevelModel extends Model
{
    use HasFactory;

    protected $table = 'product_level';
    protected $primaryKey = 'id';

    //这模型没有时间戳
    public $timestamps = false;

    protected $fillable = [
        'id',
        'category_id',
        'name_cn',
        'name_en',
        'status',
    ];


    /**
     * 删除动作为改状态
     * @return true
     */
    public function delete(): true
    {
        $this->status = 0;
        $this->save();
        return true;
    }

}
