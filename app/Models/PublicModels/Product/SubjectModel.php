<?php

namespace App\Models\PublicModels\Product;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubjectModel extends Model
{
    use HasFactory;

    protected $table = 'subject';
    protected $primaryKey = 'id';
    //这模型没有时间戳
    public $timestamps = false;

    protected $fillable = [
        'name_cn',
        'name_en',
        'status',
    ];
}
