<?php

namespace App\Models\PublicModels\Product;

use App\Models\PublicModels\BuModel;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * Class ProductCategoryModel
 *
 * 产品类别模型
 *
 * @property int $id 主键ID
 * @property int $bu_id 产品线
 * @property int $value_chain_id 价值链
 * @property string $name_cn 类别中文名称
 * @property string $name_en 类别英文名称
 * @property int $type 类别类型：1-课程类，2-计划类，3-服务类，4-其他类，5-咨询类
 * @property int $status 状态：0-禁用，1-启用
 * @property string|null $memo 备注
 * @property int $created_by 创建人ID
 * @property int $updated_by 更新人ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 *
 * @property-read BuModel $bu 关联的产品线
 * @property-read Collection|ProductLevelModel[] $levels 关联的业务分级
 */
class ProductCategoryModel extends Model
{
    use HasFactory;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'product_category';

    /**
     * 主键名
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'bu_id',
        'name_cn',
        'name_en',
        'type',
        'status',
        'memo',
        'sort',
        'created_by',
        'updated_by'
    ];

    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 产品类型：课程类
     */
    public const int TYPE_COURSE = 1;

    /**
     * 产品类型：计划类
     */
    public const int TYPE_PLAN = 2;

    /**
     * 产品类型：服务类
     */
    public const int TYPE_SERVICE = 3;

    /**
     * 产品类型：其他类
     */
    public const int TYPE_OTHER = 4;

    /**
     * 产品类型：咨询类
     */
    public const int TYPE_CONSULT = 5;

    /**
     * 产品类型中文描述
     *
     * @var array<int, string>
     */
    public const array TYPE_DESCRIPTION_CN = [
        self::TYPE_COURSE => '课程类',
        self::TYPE_PLAN => '计划类',
        self::TYPE_SERVICE => '服务类',
        self::TYPE_OTHER => '其他类',
        self::TYPE_CONSULT => '咨询类'
    ];

    /**
     * 产品类型英文描述
     *
     * @var array<int, string>
     */
    public const array TYPE_DESCRIPTION_EN = [
        self::TYPE_COURSE => 'Course',
        self::TYPE_PLAN => 'Plan',
        self::TYPE_SERVICE => 'Service',
        self::TYPE_OTHER => 'Other',
        self::TYPE_CONSULT => 'Consult'
    ];


    /**
     * 获取产品线
     *
     * @return BelongsTo
     */
    public function bu(): BelongsTo
    {
        return $this->belongsTo(BuModel::class, 'bu_id', 'id');
    }


    /**
     * 获取产品业务分级
     * @return HasMany
     */
    public function levels(): HasMany
    {
        return $this->hasMany(ProductLevelModel::class, 'category_id', 'id');
    }
}
