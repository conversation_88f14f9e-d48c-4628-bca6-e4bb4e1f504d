<?php

namespace App\Models\PublicModels\Product;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductCategoryFactorModel extends Model
{
    use HasFactory;

    protected $table = 'product_category_factor';
    protected $primaryKey = 'id';
    //这模型没有时间戳
    public $timestamps = false;

    protected $fillable = [
        'category_id',
        'pricing_factor_id',
    ];
}
