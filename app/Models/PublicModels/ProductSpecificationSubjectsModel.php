<?php

namespace App\Models\PublicModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductSpecificationSubjectsModel extends Model
{
    use HasFactory;

    protected $table = 'product_specification_subjects';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'specification_id',
        'subject_id',
        'quantity',
        'nct_quantity',
    ];

    public function subject(): BelongsTo
    {
        return $this->belongsTo(SubjectModel::class, 'subject_id', 'id');
    }
}
