<?php

namespace App\Models\PublicModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ProductSpecificationFactorsModel extends Model
{
    use HasFactory;

    protected $table = 'product_specification_factors';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'id',
        'specification_id',
        'pricing_factor_id',
        'value_id',
    ];


}
