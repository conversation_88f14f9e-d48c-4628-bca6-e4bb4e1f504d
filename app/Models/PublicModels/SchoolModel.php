<?php

namespace App\Models\PublicModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class SchoolModel extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'school';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    protected array $cast = [
        'course_system' => 'array',
    ];


    /**
     * 获取学校拥有的课程体系。
     */
    public function getCourseSystems(): array
    {
        return DB::table('course_system')->whereIn('id', json_decode($this->course_system))->pluck('name', 'id')->toArray();
    }


}
