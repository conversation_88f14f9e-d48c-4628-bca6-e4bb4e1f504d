<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram 助手测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #0f0f0f;
            color: #fff;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .mock-telegram {
            background: #1e1e1e;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .chat-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #0088cc;
        }
        
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #333;
            padding: 10px;
            margin-bottom: 15px;
            background: #2a2a2a;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px;
            background: #333;
            border-radius: 4px;
        }
        
        .sender-name {
            font-weight: bold;
            color: #0088cc;
            margin-bottom: 4px;
        }
        
        .input-area {
            display: flex;
            gap: 10px;
        }
        
        #editable-message-text {
            flex: 1;
            padding: 10px;
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            color: #fff;
            min-height: 40px;
            outline: none;
        }
        
        .send-button {
            padding: 10px 20px;
            background: #0088cc;
            border: none;
            border-radius: 4px;
            color: #fff;
            cursor: pointer;
        }
        
        .send-button:hover {
            background: #0077bb;
        }
        
        .instructions {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            color: #0088cc;
            margin-top: 0;
        }
        
        .instructions ul {
            line-height: 1.6;
        }
        
        .warning {
            background: #ffc107;
            color: #000;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Telegram 助手测试页面</h1>
            <p>这是一个模拟的 Telegram 界面，用于测试 Telegram 助手脚本</p>
        </div>
        
        <div class="warning">
            ⚠️ 注意：这只是一个测试页面。实际使用时，请在 web.telegram.org 上安装和使用脚本。
        </div>
        
        <div class="instructions">
            <h3>使用说明</h3>
            <ul>
                <li>安装 Tampermonkey 浏览器扩展</li>
                <li>将 <code>app/tg.js</code> 文件内容复制到 Tampermonkey 中创建新脚本</li>
                <li>修改脚本的 <code>@match</code> 规则为 <code>https://web.telegram.org/*</code></li>
                <li>保存脚本并访问 <a href="https://web.telegram.org" target="_blank">web.telegram.org</a></li>
                <li>脚本会在页面右下角显示一个蓝色的 📱 图标</li>
                <li>点击图标打开配置界面，设置群组、消息和定时参数</li>
            </ul>
        </div>
        
        <div class="mock-telegram">
            <div class="chat-title">测试群组</div>
            <div class="messages" id="messages">
                <div class="message">
                    <div class="sender-name">用户1</div>
                    <div>大家好！</div>
                </div>
                <div class="message">
                    <div class="sender-name">用户2</div>
                    <div>Hello everyone!</div>
                </div>
            </div>
            <div class="input-area">
                <div id="editable-message-text" contenteditable="true" placeholder="输入消息..."></div>
                <button class="send-button" onclick="sendTestMessage()">发送</button>
            </div>
        </div>
        
        <div class="instructions">
            <h3>功能特性</h3>
            <ul>
                <li><strong>群组管理</strong>：添加和管理要监听的群组</li>
                <li><strong>消息配置</strong>：设置要发送的消息内容和模板</li>
                <li><strong>定时发送</strong>：支持固定间隔、指定时间和随机间隔</li>
                <li><strong>关键词监听</strong>：监听特定关键词并触发操作</li>
                <li><strong>用户监听</strong>：监听特定用户的消息</li>
                <li><strong>变量替换</strong>：支持时间、日期、随机数等变量</li>
                <li><strong>安全控制</strong>：紧急停止、发送确认、每日限制</li>
                <li><strong>日志记录</strong>：详细的操作日志和统计信息</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>配置示例</h3>
            <h4>消息模板：</h4>
            <ul>
                <li>早安！今天是 {date}</li>
                <li>当前时间：{time}</li>
                <li>随机数：{random}</li>
            </ul>
            
            <h4>定时设置：</h4>
            <ul>
                <li>每30分钟发送一次</li>
                <li>每天9:00和18:00发送</li>
                <li>随机间隔30-120分钟</li>
            </ul>
            
            <h4>监听关键词：</h4>
            <ul>
                <li>签到</li>
                <li>打卡</li>
                <li>通知</li>
            </ul>
        </div>
    </div>
    
    <script>
        function sendTestMessage() {
            const input = document.getElementById('editable-message-text');
            const messages = document.getElementById('messages');
            const text = input.textContent.trim();
            
            if (text) {
                const messageEl = document.createElement('div');
                messageEl.className = 'message';
                messageEl.innerHTML = `
                    <div class="sender-name">我</div>
                    <div>${text}</div>
                `;
                messages.appendChild(messageEl);
                messages.scrollTop = messages.scrollHeight;
                
                input.textContent = '';
            }
        }
        
        // 模拟按回车发送
        document.getElementById('editable-message-text').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendTestMessage();
            }
        });
        
        // 添加一些测试消息
        setTimeout(() => {
            const messages = document.getElementById('messages');
            const testMessages = [
                { sender: '用户3', text: '签到' },
                { sender: '用户4', text: '今天天气不错' },
                { sender: '用户5', text: '大家记得打卡哦' }
            ];
            
            testMessages.forEach((msg, index) => {
                setTimeout(() => {
                    const messageEl = document.createElement('div');
                    messageEl.className = 'message';
                    messageEl.innerHTML = `
                        <div class="sender-name">${msg.sender}</div>
                        <div>${msg.text}</div>
                    `;
                    messages.appendChild(messageEl);
                    messages.scrollTop = messages.scrollHeight;
                }, index * 2000);
            });
        }, 3000);
    </script>
</body>
</html>
