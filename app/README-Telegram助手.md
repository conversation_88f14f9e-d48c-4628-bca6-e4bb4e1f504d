# 📱 Telegram 定时消息助手

一个功能强大的 Telegram Web 用户脚本，支持定时发送消息、关键词监听、群组管理等功能。

## ✨ 主要功能

### 🎯 核心功能
- **定时发送消息**：支持固定间隔、指定时间、随机间隔三种模式
- **群组管理**：添加和管理要监听的群组
- **消息模板**：预设常用消息模板，支持随机选择
- **变量替换**：支持时间、日期、随机数等动态变量
- **关键词监听**：监听特定关键词并触发通知
- **用户监听**：监听特定用户的消息

### 🛡️ 安全功能
- **紧急停止**：一键停止所有自动化操作
- **发送确认**：发送前弹出确认对话框
- **每日限制**：设置每日最大发送次数
- **工作日模式**：仅在工作日执行定时任务

### 📊 监控功能
- **操作日志**：详细记录所有操作和错误
- **统计信息**：显示发送成功/失败次数
- **实时状态**：显示当前运行状态

## 🚀 安装使用

### 1. 安装用户脚本管理器
推荐使用 [Tampermonkey](https://www.tampermonkey.net/)：
- Chrome: [Chrome Web Store](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- Firefox: [Firefox Add-ons](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)
- Safari: [App Store](https://apps.apple.com/us/app/tampermonkey/id1482490089)

### 2. 安装脚本
1. 复制 `app/tg.js` 文件的全部内容
2. 打开 Tampermonkey 管理面板
3. 点击 "创建新脚本"
4. 粘贴代码并保存
5. 确保脚本已启用

### 3. 使用脚本
1. 访问 [web.telegram.org](https://web.telegram.org)
2. 等待页面完全加载
3. 在右下角会出现蓝色的 📱 图标
4. 点击图标打开配置界面

## 📖 使用指南

### 群组配置
1. 进入要监听的群组聊天
2. 点击助手图标打开配置界面
3. 在"群组"标签页点击"添加当前群组"
4. 群组将被保存到列表中

### 消息配置
1. 在"消息"标签页输入要发送的消息
2. 可以添加多个消息模板
3. 启用"使用随机模板"会随机选择模板发送
4. 启用"启用变量替换"支持以下变量：
   - `{time}` - 当前时间 (如: 14:30:25)
   - `{date}` - 当前日期 (如: 2025/8/27)
   - `{datetime}` - 日期时间 (如: 2025/8/27 14:30:25)
   - `{random}` - 随机数 (0-999)
   - `{timestamp}` - 时间戳

### 定时设置
#### 固定间隔模式
- 设置发送间隔（分钟）
- 例如：每30分钟发送一次

#### 指定时间模式
- 添加具体的发送时间点
- 例如：每天9:00和18:00发送
- 支持添加多个时间点

#### 随机间隔模式
- 设置最小和最大间隔时间
- 例如：30-120分钟随机间隔
- 有助于避免被检测为机器人

### 监听配置
#### 关键词监听
- 添加要监听的关键词
- 支持三种匹配模式：
  - **包含**：消息包含关键词即触发
  - **完全匹配**：消息完全等于关键词
  - **正则表达式**：使用正则表达式匹配

#### 用户监听
- 添加要监听的用户名
- 只有指定用户的消息才会触发监听

### 高级选项
- **仅工作日**：只在周一到周五执行定时任务
- **发送前确认**：每次发送前弹出确认对话框
- **每日最大发送数**：防止过度发送，保护账号安全

## 🎨 界面说明

### 主界面
- **群组**：管理监听的群组列表
- **消息**：配置发送的消息内容和模板
- **定时**：设置定时发送规则
- **监听**：配置关键词和用户监听
- **日志**：查看操作日志和统计信息

### 状态指示
- 🟢 绿色：正常运行
- 🟡 黄色：警告状态
- 🔴 红色：错误或紧急停止

### 控制按钮
- **启动定时**：开始执行定时任务
- **停止定时**：停止定时任务
- **紧急停止**：立即停止所有操作
- **测试发送**：测试消息发送功能

## ⚠️ 注意事项

### 使用建议
1. **适度使用**：避免频繁发送消息，以免被Telegram限制
2. **内容合规**：确保发送的消息内容符合相关法律法规
3. **账号安全**：建议设置合理的发送间隔和每日限制
4. **备份配置**：重要配置建议手动备份

### 技术限制
1. 仅支持 Telegram Web 版本
2. 需要保持浏览器标签页打开
3. 依赖页面DOM结构，Telegram更新可能影响功能
4. 不支持发送图片、文件等媒体内容

### 故障排除
1. **脚本不工作**：检查是否在正确的域名下运行
2. **无法发送消息**：确认当前在聊天界面且有发送权限
3. **定时不准确**：浏览器休眠可能影响定时器精度
4. **配置丢失**：检查浏览器是否允许脚本存储数据

## 🔧 开发说明

### 文件结构
```
app/
├── tg.js              # 主脚本文件
├── test-tg.html       # 测试页面
└── README-Telegram助手.md  # 说明文档
```

### 主要类
- `TelegramConfig`：配置管理
- `TelegramLogger`：日志记录
- `TelegramOperator`：Telegram操作
- `TelegramScheduler`：定时任务调度
- `TelegramUI`：用户界面
- `TelegramApp`：主应用程序

### 数据存储
使用 Tampermonkey 的 `GM_setValue` 和 `GM_getValue` API 存储配置和日志数据。

## 📄 许可证

本项目仅供学习和个人使用，请遵守相关法律法规和平台服务条款。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

---

**免责声明**：本工具仅供学习和研究使用，使用者需自行承担使用风险，开发者不对任何损失负责。
