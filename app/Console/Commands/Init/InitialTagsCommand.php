<?php

namespace App\Console\Commands\Init;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class InitialTagsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:initial-tags {--rollback}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化标签信息';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        if ($this->option('rollback')) {
            DB::table('tag')->truncate();
            DB::table('tag_relation')->truncate();
        } else {
            $crm2DbName = env('CRM2_DB_NAME', 'cas_crm');
            // 写入产品组标签
            DB::statement("INSERT INTO crm_tag (id,name_cn,resource_type,description,`status`,created_by,updated_by,created_at,updated_at)
SELECT groupid,`name`,1 AS resource_type,description,IF(groupid=1,0,`status`) AS `status`,creatorid,creatorid,createtime,modifytime FROM {$crm2DbName}.crm_product_group");
            // 写入客户组标签
            DB::statement("INSERT INTO crm_tag (name_cn,resource_type,function_name,function_params,description,`status`,created_by,updated_by,created_at,updated_at)
SELECT `name`,2 AS resource_type,source AS function_name,params AS function_params,description,`status`,creatorid,creatorid,createtime,modifytime FROM {$crm2DbName}.crm_customer_group");
            // 写入产品标签
            DB::statement("INSERT INTO crm_tag_relation (tag_id,resource_id,resource_type,created_by,updated_by,created_at,updated_at) SELECT groupid AS tag_id,specificationid AS resource_id,1 AS resource_type,1 AS created_by,1 AS updated_by,addtime AS created_at,addtime AS updated_at FROM {$crm2DbName}.crm_product_group2specification WHERE groupid !=1 ORDER BY addtime");
        }
    }
}
