<?php

namespace App\Console\Commands\Init;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class InitialPublicInfoCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:initial-public-info {--rollback}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化通用类信息';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $crm2DbName = env('CRM2_DB_NAME', 'cas_crm');
        if ($this->option('rollback')) {
            DB::table('organization')->truncate();
            DB::table('organization_region')->truncate();
            DB::table('campus')->truncate();
        } else {
            DB::table('organization')->insert([
                [
                    'id' => 1,
                    'name' => '浙江新课堂教育股份有限公司',
                    'phone' => '88906699',
                    'address' => null,
                    'if_ea' => 0, // 对应 is_ea
                    'status' => 1,
                    'ea_phone' => '+86（0）571 88906699',
                    'if_financial_set' => 1
                ],
                [
                    'id' => 2,
                    'name' => '浙江笑果教育科技有限公司',
                    'phone' => '88906699',
                    'address' => null,
                    'if_ea' => 0,
                    'status' => 1,
                    'ea_phone' => '+86（0）571 88906699',
                    'if_financial_set' => 1
                ],
                [
                    'id' => 3,
                    'name' => '浙江新课堂久年教育咨询有限公司',
                    'phone' => '88906699',
                    'address' => null,
                    'if_ea' => 0,
                    'status' => 1,
                    'ea_phone' => '+86（0）571 88906699',
                    'if_financial_set' => 1
                ],
                [
                    'id' => 4,
                    'name' => '杭州新课堂培训学校',
                    'phone' => '88906699',
                    'address' => null,
                    'if_ea' => 0,
                    'status' => 1,
                    'ea_phone' => '+86（0）571 88906699',
                    'if_financial_set' => 0
                ],
                [
                    'id' => 5,
                    'name' => '浙江磨洋工高科技有限公司',
                    'phone' => '88906699',
                    'address' => '中国浙江省杭州市西湖区文三路90号东部软件园科技广场楼4&5层',
                    'if_ea' => 1,
                    'status' => 1,
                    'ea_phone' => '+86（0）571 88906699',
                    'if_financial_set' => 1
                ],
                [
                    'id' => 6,
                    'name' => '浙江百子善教育咨询有限公司',
                    'phone' => '88906699',
                    'address' => null,
                    'if_ea' => 0,
                    'status' => 1,
                    'ea_phone' => '+86（0）571 88906699',
                    'if_financial_set' => 1
                ],
                [
                    'id' => 7,
                    'name' => '杭州新课堂教育咨询有限公司',
                    'phone' => '88906699',
                    'address' => null,
                    'if_ea' => 0,
                    'status' => 1,
                    'ea_phone' => '+86（0）571 88906699',
                    'if_financial_set' => 1
                ],
                [
                    'id' => 8,
                    'name' => '上海新课堂匹优教育科技有限公司',
                    'phone' => '88906699',
                    'address' => '中国上海市徐汇区漕溪北路375号中金国际广场C座7层',
                    'if_ea' => 1,
                    'status' => 1,
                    'ea_phone' => '+86（0）152 67434447',
                    'if_financial_set' => 1
                ],
                [
                    'id' => 9,
                    'name' => '深圳新课堂匹优国际教育服务有限公司',
                    'phone' => '88906699',
                    'address' => '中国广东省深圳市南山区海德一道88号中洲控股金融中心A座7层',
                    'if_ea' => 1,
                    'status' => 1,
                    'ea_phone' => '+86（0）755 82579066',
                    'if_financial_set' => 1
                ],
                [
                    'id' => 10,
                    'name' => 'THINKTOWN EDUCATION INC.',
                    'phone' => '88906699',
                    'address' => 'Floor 23, 1330 Avenue of the Americas, New York, NY 10019',
                    'if_ea' => 0,
                    'status' => 1,
                    'ea_phone' => '******-701-3505',
                    'if_financial_set' => 1
                ],
                [
                    'id' => 11,
                    'name' => '上海新课堂匹优培训学校有限公司',
                    'phone' => '88906699',
                    'address' => null,
                    'if_ea' => 0,
                    'status' => 1,
                    'ea_phone' => null,
                    'if_financial_set' => 1
                ],
                [
                    'id' => 12,
                    'name' => '杭州新课堂培训学校有限公司',
                    'phone' => '88906699',
                    'address' => null,
                    'if_ea' => 0,
                    'status' => 1,
                    'ea_phone' => '+86（0）571 88906699',
                    'if_financial_set' => 1
                ],
                [
                    'id' => 13,
                    'name' => '北京新课堂匹优教育科技有限公司',
                    'phone' => '88906699',
                    'address' => '中国北京市海淀区北四环西路56号辉煌时代大厦3层03-153',
                    'if_ea' => 1,
                    'status' => 1,
                    'ea_phone' => '+86（0）10 53518608',
                    'if_financial_set' => 1
                ]
            ]);

            DB::table('organization_region')->insert([
                ['id' => 1, 'name_cn' => '杭州', 'name_en' => 'Hangzhou', 'status' => 1],
                ['id' => 2, 'name_cn' => '纽约', 'name_en' => 'NYC', 'status' => 1],
                ['id' => 3, 'name_cn' => '上海', 'name_en' => 'Shanghai', 'status' => 1],
                ['id' => 4, 'name_cn' => '深圳', 'name_en' => 'Shenzhen', 'status' => 1],
                ['id' => 5, 'name_cn' => '北京', 'name_en' => 'Beijing', 'status' => 1],
                ['id' => 6, 'name_cn' => '全球', 'name_en' => 'Global', 'status' => 1],
            ]);

            DB::table('campus')->insert([
                ['id' => 1, 'name' => '杭州文三路校区', 'region_id' => 1, 'status' => 1, 'oa_school_id' => 2],
                ['id' => 2, 'name' => '杭州文化官校区', 'region_id' => 1, 'status' => 0, 'oa_school_id' => 7],
                ['id' => 3, 'name' => '杭州庆春路校区', 'region_id' => 1, 'status' => 0, 'oa_school_id' => 5],
                ['id' => 4, 'name' => '杭州滨江逸天校区', 'region_id' => 1, 'status' => 0, 'oa_school_id' => 8],
                ['id' => 5, 'name' => '杭州莲花街校区', 'region_id' => 1, 'status' => 0, 'oa_school_id' => 6],
                ['id' => 6, 'name' => '杭州梅家坞校区', 'region_id' => 1, 'status' => 0, 'oa_school_id' => null],
                ['id' => 7, 'name' => '杭州滨江中恒校区', 'region_id' => 1, 'status' => 0, 'oa_school_id' => null],
                ['id' => 8, 'name' => '纽约校区', 'region_id' => 2, 'status' => 1, 'oa_school_id' => 4],
                ['id' => 9, 'name' => '上海校区', 'region_id' => 3, 'status' => 1, 'oa_school_id' => 3],
                ['id' => 10, 'name' => '深圳校区', 'region_id' => 4, 'status' => 1, 'oa_school_id' => 10],
                ['id' => 11, 'name' => '杭州钱江新城校区', 'region_id' => 1, 'status' => 1, 'oa_school_id' => 13],
                ['id' => 12, 'name' => '杭州近江校区', 'region_id' => 1, 'status' => 1, 'oa_school_id' => 14],
                ['id' => 13, 'name' => '杭州滨江校区', 'region_id' => 1, 'status' => 0, 'oa_school_id' => 12],
                ['id' => 14, 'name' => '北京校区', 'region_id' => 5, 'status' => 1, 'oa_school_id' => 15],
                ['id' => 15, 'name' => '杭州西溪校区', 'region_id' => 1, 'status' => 1, 'oa_school_id' => 16],
                ['id' => 16, 'name' => '兼职远程办公', 'region_id' => 0, 'status' => 0, 'oa_school_id' => 17],
                ['id' => 17, 'name' => '兼职远程办公', 'region_id' => 0, 'status' => 0, 'oa_school_id' => 24],
                ['id' => 18, 'name' => '兼职远程办公', 'region_id' => 0, 'status' => 0, 'oa_school_id' => 27],
                ['id' => 20, 'name' => '全球', 'region_id' => 6, 'status' => 1, 'oa_school_id' => null]
            ]);

        }
    }
}
