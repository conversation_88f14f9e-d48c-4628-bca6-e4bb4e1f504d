<?php

namespace App\Console\Commands\Init;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class InitialCustomerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:initial-customer {--rollback}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化客户数据';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $crm2DbName = env('CRM2_DB_NAME', 'cas_crm');
        if ($this->option('rollback')) {
            DB::table('customer')->truncate();
            DB::table('customer_relation')->truncate();
        } else {
            // 写入客户基础数据
            DB::statement("INSERT INTO crm_customer (id,ua_id,ua_mobile,wx_id,`code`,`name`,english_name,is_from_lead,gender,customer_stage,description,training_status,study_status,source,source_detail,source_memo,phone,phone_owner,other_contact,other_contact_detail,school,grade,school_year,is_intention,course_system,target_school,abroad_intention,major_intention,is_in_xkt,other_institution,apply_year,apply_season,lose_reason,total_usd,total_rmb,created_by,updated_by,deleted_at,created_at,updated_at)
SELECT va.accountid,va.ua_id,va.ua_mobile,va.wxid,va.smpid AS `code`,va.accountname AS `name`,va.english_name,va.isconvertedfromlead AS is_from_lead,IF(va.account_type='Male',0,1) AS gender,2 AS customer_stage,vca.description,va.accountstate AS training_status,va.cf_1652 AS study_status,va.rating AS source,va.sourcedetail AS source_detail,va.sourcememo AS source_memo,IF(va.phone='NULL' OR va.phone='待确认',NULL,va.phone) AS phone,va.contactrelation AS phone_owner,va.contact_1 AS other_contact,va.contact_2 AS other_contact_detail,IF(va.school='',NULL,cast(va.school AS SIGNED)) AS school,(
SELECT id FROM crm_grade WHERE grade_name=va.grade) AS grade,va.school_year,va.is_intention,vaf.cf_1654 AS course_system,va.tendtocollege AS target_school,va.abroadintension AS abroad_intention,va.majorintention AS major_intention,va.isinthinktown AS is_in_xkt,va.otherinstitutions AS other_institution,va.apply_year,va.apply_month AS apply_season,va.lose_reason,va.total_usd,va.total_rmb,vca.smcreatorid AS created_by,vca.modifiedby AS updated_by,IF(vca.deleted=1,vca.modifiedtime,NULL) AS deleted_at,vca.createdtime AS created_at,vca.modifiedtime AS updated_at FROM {$crm2DbName}.vtiger_account va JOIN {$crm2DbName}.vtiger_crmentity vca ON vca.crmid=va.accountid JOIN {$crm2DbName}.vtiger_accountscf vaf ON vaf.accountid=va.accountid");
            // 写入客户关系
            DB::statement("INSERT INTO crm_customer_relation (customer_id,user_id,start_time,end_time,memo,type,`status`,created_by,updated_by,created_at,updated_at)
SELECT va.accountid AS customer_id,vca.smownerid AS user_id,vca.createdtime AS start_date,'2099-12-20 00:00:00' AS end_time,NULL AS memo,1 AS type,1 AS `status`,vca.smcreatorid AS created_by,vca.modifiedby AS updated_by,vca.createdtime AS created_at,vca.modifiedtime AS updated_at FROM {$crm2DbName}.vtiger_account va JOIN {$crm2DbName}.vtiger_crmentity vca ON vca.crmid=va.accountid UNION ALL
SELECT a2s.accountid AS customer_id,a2s.userid AS user_id,vp.startdate AS start_time,IF(vp.enddate IS NULL OR CAST(vp.enddate AS CHAR(20))='0000-00-00 00:00:00','2099-12-20 00:00:00',vp.enddate) AS end_time,vp.memo AS memo,2 AS `type`,IF(vp.permitstatus='Active',1,0) AS `status`,vcp.smcreatorid AS created_by,vcp.modifiedby AS updated_by,vcp.createdtime AS created_at,vcp.modifiedtime AS updated_at FROM {$crm2DbName}.vtiger_account2server a2s JOIN {$crm2DbName}.vtiger_permit vp ON vp.permitid=a2s.permitid JOIN {$crm2DbName}.vtiger_crmentity vcp ON vcp.crmid=vp.permitid UNION ALL
SELECT a2c.accountid AS customer_id,a2c.userid AS user_id,vp.startdate AS start_time,IF(vp.enddate IS NULL OR CAST(vp.enddate AS CHAR(20))='0000-00-00 00:00:00','2099-12-20 00:00:00',vp.enddate) AS end_time,vp.memo AS memo,3 AS `type`,IF(vp.permitstatus='Active',1,0) AS `status`,vcp.smcreatorid AS created_by,vcp.modifiedby AS updated_by,vcp.createdtime AS created_at,vcp.modifiedtime AS updated_at FROM {$crm2DbName}.vtiger_account2consultant a2c JOIN {$crm2DbName}.vtiger_permit vp ON vp.permitid=a2c.permitid JOIN {$crm2DbName}.vtiger_crmentity vcp ON vcp.crmid=vp.permitid");
            //写入线索数据
            DB::statement("INSERT INTO crm_customer (id,wx_id,`code`,`name`,english_name,is_from_lead,gender,customer_stage,description,follow_up_status,source,source_detail,source_memo,phone,phone_owner,other_contact,other_contact_detail,school,grade,target_campus,consulting_course,total_usd,total_rmb,created_by,updated_by,deleted_at,created_at,updated_at)
SELECT vl.leadid,vl.wxid AS wxid,vl.smpid AS `code`,vl.lastname AS `name`,vl.english_name,1 AS is_from_lead,IF(vl.gender='Male',0,1) AS gender,1 AS customer_stage,vcl.description,vl.followupstatus,vl.rating AS source,vl.sourcedetail AS source_detail,vl.sourcememo AS source_memo,IF(vl.phone='NULL' OR vl.phone='待确认',NULL,vl.phone) AS phone,vl.contactrelation AS phone_owner,vl.contact_1 AS other_contact,vl.contact_2 AS other_contact_detail,IF(vl.school='',NULL,cast(vl.school AS SIGNED)) AS school,(
SELECT id FROM crm_grade WHERE grade_name=vl.grade) AS grade,(
SELECT campusid FROM {$crm2DbName}.vtiger_campus WHERE campus=vl.campus) AS campus,vl.consultingcourse,0 AS total_usd,0 AS total_rmb,vcl.smcreatorid AS created_by,vcl.modifiedby AS updated_by,IF(vcl.deleted=1,vcl.modifiedtime,NULL) AS deleted_at,vcl.createdtime AS created_at,vcl.modifiedtime AS updated_at FROM {$crm2DbName}.vtiger_leaddetails vl JOIN {$crm2DbName}.vtiger_crmentity vcl ON vcl.crmid=vl.leadid WHERE vl.converted=0 AND vl.leadid !=538002");
            //写入线索关系
            DB::statement("INSERT INTO crm_customer_relation (customer_id,user_id,start_time,end_time,memo,type,`status`,created_by,updated_by,created_at,updated_at)
SELECT vl.leadid AS customer_id,vcl.smownerid AS user_id,vcl.createdtime AS start_date,'2099-12-20 00:00:00' AS end_time,NULL AS memo,1 AS type,1 AS `status`,vcl.smcreatorid AS created_by,vcl.modifiedby AS updated_by,vcl.createdtime AS created_at,vcl.modifiedtime AS updated_at FROM {$crm2DbName}.vtiger_leaddetails vl JOIN {$crm2DbName}.vtiger_crmentity vcl ON vcl.crmid=vl.leadid WHERE vl.converted=0 AND vl.leadid !=538002");

        }

    }
}
