<?php

namespace App\Console\Commands\Init;

use App\Http\Service\DepartmentService\UserService;
use App\Http\Service\DepartmentService\YkService;
use App\Models\DepartmentModels\UserModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class YkGetUsersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:yk-get-users {--initialize : 是否初始化用户数据}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化/维护全系统的所有用户信息数据';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $crm2DbName = env('CRM2_DB_NAME', 'cas_crm');
        if ($this->option('initialize')) {
            $this->info(date("Y-m-d H:i:s").' 开始初始化用户数据');
            DB::statement("TRUNCATE TABLE crm_users");
            DB::statement("INSERT INTO crm_users (id,`name`,user_code,ua_id,is_admin,oa_id,user_status,crm_depart_id,campus)
SELECT vu.id,vu.last_name,vu.user_name,xui.uaid,IF(vu.is_admin='on',1,0),xui.oaid,IF(vu.`status`='Active',1,0),(
SELECT teamid FROM {$crm2DbName}.vtiger_user2team WHERE userid=vu.id LIMIT 1),(
SELECT t.campusid FROM {$crm2DbName}.vtiger_user2team vu2t JOIN {$crm2DbName}.vtiger_team t ON vu2t.teamid=t.teamid WHERE vu2t.userid=vu.id LIMIT 1) FROM {$crm2DbName}.vtiger_users vu JOIN {$crm2DbName}.xkt_user_ids xui ON vu.id=xui.crmid");
            DB::statement("INSERT INTO crm_users (id,`name`,user_code,ua_id,is_admin,oa_id,user_status,crm_depart_id,campus) SELECT vu.id,vu.last_name,vu.user_name,'',0,0,0,1,20 FROM {$crm2DbName}.vtiger_users vu WHERE vu.id = 1");
            $this->info(date("Y-m-d H:i:s").' 初始化用户数据完成');
        }

        $campus = DB::table('campus')->pluck('id', 'name')->toArray();
        $ykService = new YkService();

        $res = $ykService->getYkData('GetUsers');
        if (!$res['success']) {
            $this->error(date("Y-m-d H:i:s") . ' 获取企业微信在职用户信息失败' . $res['message']);
            return;
        }
        $this->info(date("Y-m-d H:i:s") . ' 获取企业微信在职用户信息成功');
        $datas = $res['data'];
        foreach ($datas as $data) {
            $campusName = $data['campus'] ?? '';
            if ($campusName == 'Thinktown America') $campusName = '纽约校区';
            $campusId = $campus[$campusName] ?? 0;
            if ($campusName == '兼职办公') $campusId = 17;

            $user = UserModel::where('user_code', $data['userid'])->first();
            if ($user) {
                $user->position = $data['position'] ?? '';
                if($user->oa_id == 0 || $user->oa_id == null){
                    $user->oa_id = $data['oaid'] ?? 0;
                }
                $user->parent_oa_id = $data['parentoaid'] ?? 0;
                $user->oa_dept_id = $data['oa_departid'] ?? 0;
                $user->ehr_depart_id = $data['ehr_departid'] ?? 0;
                if($user->ua_id == 0 || $user->ua_id == null){
                    $user->ua_id = $data['uaid'] ?? 0;
                }
                if($user->ehr_uid == 0 || $user->ehr_uid == null){
                    $user->ehr_uid = $data['ehr_uid'] ?? 0;
                }
                $user->ehr_puid = $data['ehr_puid'] ?? 0;
                $user->reset_day = $data['reset_day'] ?? '';
                $user->employ_type = $data['employ_type'] ?? '';
                $user->staff_mail = $data['email'] ?? '';
                $user->reset_day = $data['offday'] ?? '';
                $user->wx_avatar = $data['wx_avatar'] ?? '';
                $user->user_status = 1;
                if ($campusId > 0) $user->campus = $campusId;
                $user->save();
            }
        }
        $this->info(date("Y-m-d H:i:s") . ' ehr在职用户信息更新完成');

        $res = $ykService->getYkData('GetEhrUsers');
        if (!$res['success']) {
            $this->error(date("Y-m-d H:i:s") . ' 获取ehr在职用户信息失败' . $res['message']);
            return;
        }
        $this->info(date("Y-m-d H:i:s") . ' 获取ehr在职用户信息成功');
        $datas = $res['data'];
        foreach ($datas as $data) {
            $user = UserModel::where('user_code', $data['userid'])->first();
            if ($user) {
                $user->position = $data['position'] ?? '';
                $user->reset_day = $data['reset_day'] ?? '';
                $user->ehr_depart_id = $data['ehr_departid'] ?? 0;
                if ($user->ehr_uid == 0 || $user->ehr_uid == null) {
                    $user->ehr_uid = $data['ehr_uid'] ?? 0;
                }
                $user->ehr_puid = $data['ehr_puid'] ?? 0;
                $user->employ_type = $data['employ_type'] ?? '';
                $user->staff_mail = $data['email'] ?? '';
                $user->reset_day = $data['offday'] ?? '';
                $user->user_status = 1;
                $user->save();
            }
        }
        $this->info(date("Y-m-d H:i:s") . ' 企业微信在职用户信息更新完成');

        $res = $ykService->getYkData('GetResignUser');
        if (!$res['success']) {
            $this->error(date("Y-m-d H:i:s") . ' 获取离职用户信息失败' . $res['message']);
            return;
        }
        $this->info(date("Y-m-d H:i:s").' 获取离职用户信息成功');
        $datas = $res['data'];
        foreach ($datas as $data) {
            $campusName = $data['campus'] ?? '';
            if ($campusName == 'Thinktown America') $campusName = '纽约校区';
            $campusId = $campus[$campusName] ?? 0;
            if ($campusName == '兼职办公') $campusId = 17;

            $user = UserModel::where('user_code', $data['userid'])->first();
            if ($user) {
                if($user->oa_id == 0 || $user->oa_id == null){
                    $user->oa_id = $data['oaid'] > 0 ? $data['oaid'] : 0;
                }
                $user->parent_oa_id = $data['parentoaid'] > 0 ? $data['parentoaid'] : 0;
                $user->oa_dept_id = $data['oa_departid'] > 0 ? $data['oa_departid'] : 0;
                $user->ehr_depart_id = $data['ehr_departid'] ?? 0;
                if($user->ehr_uid == 0 || $user->ehr_uid == null){
                    $user->ehr_uid = $data['ehr_uid'] ?? 0;
                }
                $user->ehr_puid = $data['ehr_puid'] ?? 0;
                $user->employ_type = $data['employ_type'] ?? '';
                $user->staff_mail = $data['email'] ?? '';
                $user->reset_day = $data['offday'] ?? '';
                $user->wx_avatar = $data['wx_avatar'] ?? '';
                $user->user_status = 0;
                if ($campusId > 0) $user->campus = $campusId;
                $user->save();
            }
        }
        $this->info(date("Y-m-d H:i:s").' 离职用户信息更新完成');

        $crmUserService = new UserService();
        $this->info(date("Y-m-d H:i:s").' 开始更新历史用户 ehr_puid');
        $crmUserService->updateHistoryUsersEhrPuid();
        $this->info(date("Y-m-d H:i:s").' 历史用户 ehr_puid 更新完成');

        $this->info(date("Y-m-d H:i:s").' 开始生成用户树状 num');
        if ($crmUserService->generateUserNum()) {
            $this->info(date("Y-m-d H:i:s").' 用户树状 num 生成成功');
        } else {
            $this->error(date("Y-m-d H:i:s").' 用户树状 num 生成失败');
        }

    }
}
