<?php

namespace App\Console\Commands\Init;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncLearningPlanCommand extends Command
{
    protected $signature = 'init:learning_plan';
    protected $description = '迁移CRM2学习计划';

    public function handle(): void
    {
        $this->_learningPlan();
        $this->_learningPlanDetail();
        $this->_learningPlanScore();
    }

    private function _learningPlan(): void
    {
        $this->info('开始迁移 crm_learning_plan');
        $strCRM2DBName = env('CRM2_DB_NAME', 'cas_crm');
        $sql = "INSERT INTO crm_learning_plan
SELECT
	learningplanid,
	accountid,
	vtiger_crmentity.smcreatorid,
	contactid,
	`name`,
	school,
	grade,
	internationaltype,
	target,
    mobile,
	if(consultantdate='',null,consultantdate),
	NULL,
	vtiger_crmentity.createdtime,
	modifiedtime
FROM
	$strCRM2DBName.vtiger_learningplan
	LEFT JOIN $strCRM2DBName.vtiger_crmentity ON vtiger_learningplan.learningplanid = vtiger_crmentity.crmid";
        DB::statement($sql);
    }

    public function _learningPlanDetail(): void
    {
        $this->info('开始迁移 crm_learning_plan_detail');
        $strCRM2DBName = env('CRM2_DB_NAME', 'cas_crm');
        #原始附属表记录
        $sql = "INSERT INTO crm_learning_plan_detail (learning_plan_id,period,arrangement,target,memo,deleted_at)
SELECT
	learningplanid,
    IF(LENGTH(period)>255,LEFT(period, 255),period),
	arrangement,
	target,
	memo,
	if(`status`=1,null,NOW())
FROM
	$strCRM2DBName.crm_learningplan_detail";
        DB::statement($sql);
    }

    public function _learningPlanScore(): void
    {
        $this->info('开始迁移 crm_learning_plan_score');
        $strCRM2DBName = env('CRM2_DB_NAME', 'cas_crm');
        #crm_learning_plan_score
        $sql = "INSERT INTO crm_learning_plan_score (learning_plan_id,score_id,type,exam_date,score_detail,deleted_at)
SELECT
    learningplanid,
    scoreid,
    type,
    examdate,
    if(scoredetail is null,'',IF(LENGTH(scoredetail)>255,LEFT(scoredetail,255),scoredetail)),
    IF(`status`=1,null,NOW())
FROM
	$strCRM2DBName.crm_learningplan_score";
        DB::statement($sql);
        #crm_learning_plan.score_info
        $sql = "INSERT INTO crm_learning_plan_score (learning_plan_id, score_id, type, score_detail)
SELECT
	learningplanid,
	0,
	4,
	if(scoreinfo is null,'',IF(LENGTH(scoreinfo)>255,LEFT(scoreinfo,255),scoreinfo))
FROM
	$strCRM2DBName.vtiger_learningplan";
        DB::statement($sql);
    }
}
