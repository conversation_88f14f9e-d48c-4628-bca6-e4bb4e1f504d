<?php

namespace App\Console\Commands\Init;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncScoreCommand extends Command
{
    protected $signature = 'init:score';
    protected $description = '迁移CRM2成绩数据';

    public function handle(): void
    {
        $this->_scoreTemplate();
        $this->_scoreSubject();
        $this->_score();
        $this->_scoreSubitem();
        $this->_scoreFile();
    }

    private function _score(): void
    {
        $this->info('开始迁移 crm_score');
        $strCRM2DBName = env('CRM2_DB_NAME', 'cas_crm');
        $sql = "INSERT INTO crm_score
SELECT
	scoreid,
	accountid,
	templetid,
	IF(xmp_id is null,0,xmp_id) as xmp_id,
CASE
		examtype
		WHEN '真考' THEN
		1
		WHEN '模考' THEN
		2
		WHEN '其他' THEN
		3 ELSE 0
	END AS examtype,
CASE
		score_status
		WHEN '正常出分' THEN
		1
		WHEN '未参加考试' THEN
		2
		WHEN '取消成绩' THEN
		3
		WHEN '延迟出分' THEN
		4
		WHEN '不愿告知' THEN
		5 ELSE 0
	END AS score_status,
CASE
		have_writing
		WHEN 'Yes' THEN
		1
		WHEN 'No' THEN
		0 ELSE NULL
	END AS have_writing,
CASE
		is_xkt
		WHEN 'Yes' THEN
		1
		WHEN 'No' THEN
		0 ELSE NULL
	END AS is_xkt,
IF
	( target is null, '', target ) AS target,
IF
	( total is null, '', total ) AS total,
IF
( memo is null, '', memo ) AS memo,
	examdate,
	null,
	null,
	null
FROM
	{$strCRM2DBName}.vtiger_score
ORDER BY
	scoreid DESC;";
        DB::statement($sql);
    }

    private function _scoreSubitem(): void
    {
        $this->info('开始迁移 crm_score_subitem');
        $strCRM2DBName = env('CRM2_DB_NAME', 'cas_crm');
        $sql = "INSERT INTO crm_score_subitem
SELECT
	*
FROM
	$strCRM2DBName.vtiger_score_subitem";
        DB::statement($sql);
    }

    private function _scoreTemplate(): void
    {
        $this->info('开始迁移 crm_score_template');
        $strCRM2DBName = env('CRM2_DB_NAME', 'cas_crm');
        DB::table('score_template')->truncate();
        $sql = "INSERT INTO crm_score_template
SELECT
	templetid,
	templetname,
IF
( templetdescription IS NULL, '', templetdescription ),
IF
( totalscore IS NULL, '', totalscore ),
	sortorderid,
	calculate_type,
	hit,
	is_duplicate,
IF
	( templetstatus = 0, now(), NULL )
FROM
	$strCRM2DBName.xkt_score_templet";
        DB::statement($sql);
    }

    private function _scoreSubject(): void
    {
        $this->info('开始迁移 crm_score_subject');
        $strCRM2DBName = env('CRM2_DB_NAME', 'cas_crm');
        $sql = "INSERT INTO crm_score_subject
SELECT
subjectid,
templetid,
subjectname,
subjectscore,
subjectsequence,
is_count,
list,
is_must,
IF
	( `max` =- 1, NULL, `max` ),
IF
	( `min` =- 1, NULL, `min` ),
	cast(picklist as json),
	post_list_order,
	recommend_date,
IF
	( `status` = 0, now(), NULL ),
	NULL,
NULL
FROM
	$strCRM2DBName.xkt_score_subject";
        DB::statement($sql);
    }

    private function _scoreFile(): void
    {
        $this->info('开始迁移 crm_score_file');
        $strCRM2DBName = env('CRM2_DB_NAME', 'cas_crm');
        $sql = "INSERT INTO crm_upload_file (creator_by,resource_fk,resource_type,file_name,file_format,file_path)
SELECT
	xmp_id,crmid,1,`name`,`type`,CONCAT(path,`name`)
FROM
	$strCRM2DBName.vtiger_attachments
	JOIN $strCRM2DBName.vtiger_seattachmentsrel ON vtiger_attachments.attachmentsid = vtiger_seattachmentsrel.attachmentsid
	JOIN $strCRM2DBName.vtiger_score ON vtiger_score.scoreid = vtiger_seattachmentsrel.crmid";
        DB::statement($sql);
    }
}
