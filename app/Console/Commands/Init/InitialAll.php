<?php

namespace App\Console\Commands\Init;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class InitialAll extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:initial-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '割接所有数据';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        //激活laravel-admin
        Artisan::call('admin:install');
        //初始化部门
        Artisan::call('app:yk-get-dept --initialize');
        //初始化用户
        Artisan::call('init:yk-get-users --initialize');
        //初始化学校（带清理防止异常）
        Artisan::call('init:initial-school --rollback');
        Artisan::call('init:initial-school');
        //初始化产品规格（带清理防止异常）
        Artisan::call('init:initial-product --rollback');
        Artisan::call('init:initial-product');
        //初始化通用类信息
        Artisan::call('init:initial-public-info --rollback');
        Artisan::call('init:initial-public-info');
        //初始化客户
        Artisan::call('init:initial-customer --rollback');
        Artisan::call('init:initial-customer');
        //初始化标签
        Artisan::call('init:initial-tags --rollback');
        Artisan::call('init:initial-tags');

    }
}
