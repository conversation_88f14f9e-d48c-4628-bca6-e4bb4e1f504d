<?php

namespace App\Console\Commands;

use App\Entities\OA\Workflow;
use App\Entities\OA\WorkflowBaseForm;
use App\Models\FastQueryModel\FastQueryModel;
use App\Utils\OAUtil;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use phpseclib3\Crypt\RSA;

class LinsDebug extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:lins-debug';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
//       $private = RSA::createKey(1024);
//       $public = $private->getPublicKey();
//       $fingerprint = $public->getFingerprint("sha256");
//       $appid = "881ed174-a092-4248-809c-2d4ebd02907b";
//       $url = "https://oa-kf.thinktown.com";
//       $api = "/api/ec/dev/auth/regist";
//
//
//       $doReq = Http::withHeaders([
//           "appid" => $appid,
//           "cpk" => $fingerprint,
//       ])->post($url . $api);
//       dd($doReq->body());
//       $token = OAUtil::doOARequest("/api/workflow/paService/getWorkflowRequest", "5648", ["requestId" => 997513], "GET");

        $data1 = [
            ["fieldName" => "sqr", "fieldValue" => "5648"],
            ["fieldName" => "sqrq", "fieldValue" => "2025-08-23"],
            ["fieldName" => "yhqqfr", "fieldValue" => "399"],
            ["fieldName" => "yhqje", "fieldValue" => 12],
            ["fieldName" => "qkms", "fieldValue" => "ababab"],
            ["fieldName" => "xm", "fieldValue" => "243000108"],
            ["fieldName" => "gjr", "fieldValue" => "9999999"],
            ["fieldName" => "gjrtd", "fieldValue" => ""],
            ["fieldName" => "cpmc", "fieldValue" => "这是个产品"],
            ["fieldName" => "cpbh", "fieldValue" => "S062625A01"],
            ["fieldName" => "ddbh", "fieldValue" => "202506260002"],
            ["fieldName" => "ddsjzj", "fieldValue" => 3000.00],
            ["fieldName" => "Credential", "fieldValue" => ""],
            ["fieldName" => "qr", "fieldValue" => 1],
        ];
//       OAUtil::createWorkflow("5648", 318, "林德标的oa特批", $data1);
        $r = OAUtil::getWorkflow(1636335, 5648);
        $w = new WorkflowBaseForm();
        $w->requestId = Arr::get($r, "requestId");
        $w->oaId = Arr::get($r, "creatorId");
        $w->status = Arr::get($r, "status");
        $w->requestName = Arr::get($r, "requestName");
        $w->workflowId = Arr::get($r, "workflowBaseInfo.workflowId");
        foreach(Arr::get($r, "workflowMainTableInfo.requestRecords") as $field) {
            if (Arr::has($field, "workflowRequestTableFields")) {
                $w->workflowRequestTableFields = Arr::get($field, "workflowRequestTableFields");
                foreach(Arr::get($field, "workflowRequestTableFields") as $item) {
                    $w->{$item["fieldName"]} = Arr::get($item, "fieldValue");
                }
            }
        }
        $w->workflowRequestLogs =  Arr::get($r, "workflowRequestLogs");
        dd($w);
    }
}
