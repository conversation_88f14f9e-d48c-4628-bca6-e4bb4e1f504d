<?php

namespace App\Http\Controllers\PublicControllers;

use App\Http\Controllers\Controller;
use App\Http\Service\DepartmentService\CrmUserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CrmUserController extends Controller
{
    /**
     * 获取当前用户详情
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserDetail(Request $request): JsonResponse
    {
        $userInfo = new CrmUserService()->getCurrentUserDetail();
        return $this->response('', 1, $userInfo);
    }
}
