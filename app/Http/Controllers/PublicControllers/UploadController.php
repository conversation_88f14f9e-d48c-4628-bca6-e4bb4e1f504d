<?php

namespace App\Http\Controllers\PublicControllers;

use App\Http\Controllers\Controller;
use App\Http\Service\PublicService\UploadService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UploadController extends Controller
{
    public function upload(Request $request, UploadService $uploadService): JsonResponse
    {
        $file = $request->file('file');
        $type = $request->input('type');
        try {
            $rtn = $uploadService->upload($file, $type);
        } catch (\Exception $e) {
            return $this->response($e->getMessage(), 0);
        }
        return $this->response('', 1, $rtn);
    }
}
