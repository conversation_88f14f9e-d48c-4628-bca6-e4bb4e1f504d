<?php

namespace App\Http\Controllers\PublicControllers;

use App\Http\Controllers\Controller;
use App\Http\Service\DepartmentService\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UserController extends Controller
{
    /**
     * 获取当前用户详情
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserDetail(Request $request): JsonResponse
    {
        $userInfo = new UserService()->getCurrentUserDetail();
        return $this->response('', 1, $userInfo);
    }
}
