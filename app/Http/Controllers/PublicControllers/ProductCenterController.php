<?php
namespace App\Http\Controllers\PublicControllers;

use App\Http\Controllers\Controller;
use App\Http\Service\PublicService\ProductCenterService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductCenterController extends Controller {

    //产品中心列表
    public function index(Request $request, ProductCenterService $productCenterService): JsonResponse
    {
        $data = $request->all();
        return $this->success($productCenterService->getSpecificationList($data));
    }

    //检索参数获取
    public function searchParams(Request $request, ProductCenterService $productCenterService): JsonResponse
    {
        return $this->success($productCenterService->getSearchParams());
    }

}
