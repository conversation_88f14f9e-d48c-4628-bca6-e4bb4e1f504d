<?php

namespace App\Http\Controllers\EntityControllers;

use App\Http\Controllers\Controller;
use App\Http\Service\EntityService\ScoreService;
use Illuminate\Http\Request;

class ScoreController extends Controller
{
    public function index(Request $request, ScoreService $crmScoreService)
    {
        $param = $request->input();
        $data = $crmScoreService->index($param);
        return $this->response('', 1, $data);
    }

    public function show(Request $request, ScoreService $crmScoreService)
    {

    }
}
