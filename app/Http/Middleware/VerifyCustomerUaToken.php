<?php

namespace App\Http\Middleware;

use App\Http\Service\DepartmentService\UaService;
use App\Models\DepartmentModels\UserModel;
use App\Models\EntityModels\Customer\CustomerModel;
use Closure;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class VerifyCustomerUaToken extends Middleware
{

    /**
     * @param $request
     * @param Closure $next
     * @param ...$guards
     * @return Application|ResponseFactory|\Illuminate\Foundation\Application|Response|mixed
     */
    public function handle($request, Closure $next, ...$guards): mixed
    {
        $uaService = new UaService();
        $jwt = $request->cookie(env('SSO_COOKIE_KEY', 'UA_TOKEN'));
        try {
            if ($info = $uaService->verifyToken($jwt, env('SSO_SIGNATURE_KEY'))) {
                if ($info['exp'] < time()) {
                    throw new \Exception('jwt expire', -1);
                }
                if (isset($info['belong']) && $info['belong'] != 'xkt') {
                    throw new \Exception('not xkt user', -1);
                }
            } else {
                throw new \Exception('jwt error', -1);
            }
            $objUser = CustomerModel::where(['ua_id' => $info['ua_id']])->first();
            if (!$objUser) {
                throw new \Exception('user not found', -1);
            }
            Auth::login($objUser);
        } catch (\Exception $exception) {
            return response([
                'success' => -1,
                'message' => $exception->getMessage(),
                'data' => new \stdClass()
            ])->cookie(env('SSO_COOKIE_KEY', 'UA_TOKEN'), null, -1);
        }

        //设置语言
        $acceptLanguages = ['en', 'zh-CN'];
        $locale = $request->header('Accept-Language');
        if (in_array($locale, $acceptLanguages)) {
            app()->setLocale($locale);
        }
        return $next($request);
    }
}
