<?php

namespace App\Http\Service\EntityService;

use App\Models\EntityModels\Score\ScoreModel;

class ScoreService
{
    public function index($param)
    {
        $query = ScoreModel::with('template')
            ->with('file')
            ->with('subitem')
            ->with('subject');
        if (!is_null($param['template_name'])) {
            $query->whereHas('template', function ($query) use ($param) {
                $query->where('template_name', 'like', '%' . $param['template_name'] . '%');
            });
        }
        if (!is_null($param['exam_type'])) {
            $query->where('exam_type', $param['exam_type']);
        }
        if (!is_null($param['exam_date'])) {
            $query->where('exam_date', $param['exam_date']);
        }
        if (!is_null($param['subject_name'])) {
            $query->whereHas('subject', function ($query) use ($param) {
                $query->where('subject_name', 'like', '%' . $param['subject_name'] . '%');
            });
        }
        if (!is_null($param['score'])) {
            $query->where('score', $param['score']);
        }
        if (!is_null($param['created_by'])) {
            $query->where('created_by', $param['created_by']);
        }
        return $query->orderBy('id', 'desc')->paginate();
    }
}
