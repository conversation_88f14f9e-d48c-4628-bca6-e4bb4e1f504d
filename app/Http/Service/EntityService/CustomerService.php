<?php

namespace App\Http\Service\EntityService;

use App\Http\Resources\Customer\ListCollection;
use App\Models\EntityModels\Customer\CustomerModel;

class CustomerService
{

    /**
     * 获取学员列表
     * @param int $page
     * @param int $size
     * @param array $search
     * @return ListCollection
     */
    public function getCustomerList(int $page = 1, int $size = 10, array $search = []): ListCollection
    {
        // 获取当前登录用户相关信息处理数据权限
        $user = auth()->user();
        $userIds = array_unique(array_merge($user->all_employees ?? [$user->id], $user->shared_privileges ?? []));
        foreach ($user->extra_privileges ?? [] as $role) {
            $userIds = array_unique(array_merge($userIds, $role));
        }

        $query = CustomerModel::query('customer')->with(['ownerRelation.user', 'currentSchool', 'currentGrade', 'activeRelations.user'])->where('customer.is_deleted', 0)->where('customer.customer_stage', CustomerModel::CUSTOMER_STAGE_CUSTOMER);

        // 非超级管理员的用户数据权限控制
        if ($user->is_admin != 1) {
            $query->whereIn('customer.id', function ($query) use ($userIds) {
                $query->select('customer_id')->from('customer_relation')
                    ->where('status', 1)
                    ->where('start_time', '<=', now())
                    ->where('end_time', '>=', now())
                    ->whereIn('user_id', $userIds);
            });
        }

        // 处理搜索
        foreach ($search as $key => $value) {
            switch ($key) {
                case 'name':
                    $query->where('customer.name', 'like', '%' . $value . '%');
                    break;
                case 'code':
                    $query->where('customer.code', 'like', '%' . $value . '%');
                    break;
                case 'english_name':
                    $query->where('customer.english_name', 'like', '%' . $value . '%');
                    break;
                case 'code_name':
                    $query->where(function ($q) use ($value) {
                        $q->where('customer.code', 'like', '%' . $value . '%')
                            ->orWhere('customer.name', 'like', '%' . $value . '%');
                    });
                    break;
                case 'gender':
                    $query->where('customer.gender', $value);
                    break;
                case 'owner_id':
                    $query->whereHas('ownerRelation', function ($q) use ($search) {
                        // 此时，$q 是一个针对 ownerRelation 模型
                        $q->where('user_id', $search['owner_id']);
                    });
                    break;
                case 'owner_name':
                    $query->whereHas('ownerRelation.user', function ($q) use ($search) {
                        // 此时，$q 是一个针对 User 模型的查询构建器
                        $q->where('name', 'like', '%' . $search['owner_name'] . '%');
                    });
                    break;
                case 'relation_name':
                    $query->whereHas('activeRelations.user', function ($q) use ($search) {
                        // 此时，$q 是一个针对 User 模型的查询构建器
                        $q->where('name', 'like', '%' . $search['relation_name'] . '%');
                    });
                    break;
                default:
                    break;
            }
        }

        $result = $query->orderBy('customer.created_at', 'desc')->paginate($size);

        return ListCollection::make($result);
    }
}
