<?php
namespace App\Http\Service\PublicService;

use App\Http\Resources\ListCollection;
use App\Http\Resources\ProductCenter\ProductCenterListResource;
use App\Models\PublicModels\Product\PricingFactorValueModel;
use App\Models\PublicModels\Product\ProductCategoryModel;
use App\Models\PublicModels\Product\ProductSpecificationModel;
use App\Models\PublicModels\Product\SubjectModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductCenterService {

    /**
     * 获取产品类别列表
     * @param array $data
     * @return array
     */
    public function getCategoryList($data = []) {
        $query = ProductCategoryModel::with(['bu', 'levels'])
            ->where('id', '!=', 0);

        // 筛选条件
        if (!empty($data['name_cn'])) {
            $query->where('name_cn', 'like', '%' . $data['name_cn'] . '%');
        }

        if (!empty($data['name_en'])) {
            $query->where('name_en', 'like', '%' . $data['name_en'] . '%');
        }

        if (isset($data['type'])) {
            $query->where('type', $data['type']);
        }

        if (isset($data['value_chain_id'])) {
            $query->where('value_chain_id', $data['value_chain_id']);
        }

        if (isset($data['bu_id'])) {
            $query->where('bu_id', $data['bu_id']);
        }

        if (isset($data['status'])) {
            $query->where('status', $data['status']);
        }

        // 分页
        $page = $data['page'] ?? 1;
        $pageSize = $data['page_size'] ?? 50;

        $result = $query->paginate($pageSize, ['*'], 'page', $page);

        $list = $result->items();
        $formattedList = [];

        foreach ($list as $item) {
            $formattedItem = [
                'id' => $item->id,
                'name_cn' => $item->name_cn,
                'name_en' => $item->name_en,
                'type' => $item->type,
                'type_description' => ProductCategoryModel::TYPE_DESCRIPTION_CN[$item->type] ?? '',
                'value_chain_id' => $item->value_chain_id,
                'value_chain_name' => [1 => 'A', 2 => 'B', 3 => 'C', 4 => 'Other'][$item->value_chain_id] ?? '',
                'bu_id' => $item->bu_id,
                'bu_name' => $item->bu->name ?? '',
                'memo' => $item->memo,
                'status' => $item->status,
                'status_text' => $item->status == 1 ? '启用' : '禁用',
                'created_at' => $item->created_at ? $item->created_at->format('Y-m-d H:i:s') : null,
                'updated_at' => $item->updated_at ? $item->updated_at->format('Y-m-d H:i:s') : null,
                'levels' => $item->levels->map(function ($level) {
                    return [
                        'id' => $level->id,
                        'name_cn' => $level->name_cn,
                        'name_en' => $level->name_en,
                        'status' => $level->status,
                        'status_text' => $level->status == 1 ? '启用' : '禁用'
                    ];
                })->toArray()
            ];
            $formattedList[] = $formattedItem;
        }

        return [
            'list' => $formattedList,
            'total' => $result->total(),
            'current_page' => $result->currentPage(),
            'per_page' => $result->perPage(),
            'last_page' => $result->lastPage()
        ];
    }

    /**
     * 获取产品规格列表
     * @param array $data
     * @return ListCollection
     */
    public function getSpecificationList(array $data = []): ListCollection
    {
        $query = ProductSpecificationModel::with([
            'category',
            'category.bu',
            'level',
            'achievement',
            'prices' => function (HasMany $q) {
                //只拿一条
                $q->where('status', 1)->orderByDesc('start_date');
            },
            'currentPrice',
            'subjects.subject',
            'regionValue',
            'classTypeValue',
            'teacherTypeValue',
            'pricingTypeValue'
        ])->where('id', '!=', 0)
          ->orderBy('status', 'desc')
          ->orderBy('id', 'desc');

        // 筛选条件

        //产品编号/名称
        if (!empty($data['name_cn'])) {
            $query->where(function (Builder $q) use ($data) {
                $q->where('name_cn', 'like', '%' . $data['name_cn'] . '%')->orWhere('code', 'like', '%' . $data['name_cn'] . '%');
            });
        }
        //班型
        if (!empty($data['class_type'])) {
            $query->where('class_type', $data['class_type']);
        }

        //价格模式
        if (isset($data["pricing_type"])) {
            $query->where('pricing_type', $data["pricing_type"]);
        }

        //产品类别
        if (isset($data['category_id'])) {
            $query->where('category_id', $data['category_id']);
        }

        //产品线
        if (isset($data["bu_id"])) {
            $query->where('bu_id', $data['bu_id']);
        }

        //绩效类型
        if (isset($data['achievement_id'])) {
            $query->where('achievement_id', $data['achievement_id']);
        }

        //业务分级
        if (isset($data['level_id'])) {
            $query->where('level_id', $data['level_id']);
        }

        //地区
        if (isset($data['region'])) {
            $query->where('region', $data['region']);
        }

        //科目
        if (!empty($data['subject'])) {
            $query->whereHas('subjects.subject', function (Builder $q) use ($data) {
                $q->where('name_cn', "like", "%" . $data['subject'] . "%");
            });
        }
        
        //定价方案
        if (isset($data['pricing_type'])) {
            $query->where('pricing_type', $data['pricing_type']);
        }

        //状态

        if (isset($data['status'])) {
            $query->where('status', $data['status']);
        }

        // 分页
        $page = $data['page'] ?? 1;
        $pageSize = $data['page_size'] ?? 15;

        $result = $query->paginate($pageSize, ['*'], 'page', $page);

        return ListCollection::make($result, ProductCenterListResource::class);
    }

    /**
     * 获取科目列表
     * @param array $data
     * @return array
     */
    public function getSubjectList($data = []) {
        $query = SubjectModel::query();

        // 筛选条件
        if (!empty($data['name_cn'])) {
            $query->where('name_cn', 'like', '%' . $data['name_cn'] . '%');
        }

        if (!empty($data['name_en'])) {
            $query->where('name_en', 'like', '%' . $data['name_en'] . '%');
        }

        if (isset($data['status'])) {
            $query->where('status', $data['status']);
        }

        // 分页
        $page = $data['page'] ?? 1;
        $pageSize = $data['page_size'] ?? 50;

        $result = $query->paginate($pageSize, ['*'], 'page', $page);

        $list = $result->items();
        $formattedList = [];

        foreach ($list as $item) {
            $formattedItem = [
                'id' => $item->id,
                'name_cn' => $item->name_cn,
                'name_en' => $item->name_en,
                'status' => $item->status,
                'status_text' => $item->status == 1 ? '启用' : '禁用'
            ];
            $formattedList[] = $formattedItem;
        }

        return [
            'list' => $formattedList,
            'total' => $result->total(),
            'current_page' => $result->currentPage(),
            'per_page' => $result->perPage(),
            'last_page' => $result->lastPage()
        ];
    }

    public function getSearchParams() {
            
    }
}
