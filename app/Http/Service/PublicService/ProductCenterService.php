<?php
namespace App\Http\Service\PublicService;

use App\Http\Resources\ListCollection;
use App\Http\Resources\ProductCenter\ProductCenterListResource;
use App\Models\PublicModels\Product\PricingFactorValueModel;
use App\Models\PublicModels\Product\ProductCategoryModel;
use App\Models\PublicModels\Product\ProductSpecificationModel;
use App\Models\PublicModels\Product\SubjectModel;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductCenterService {

    /**
     * 获取产品类别列表
     * @param array $data
     * @return array
     */
    public function getCategoryList($data = []) {
        $query = ProductCategoryModel::with(['bu', 'levels'])
            ->where('id', '!=', 0);

        // 筛选条件
        if (!empty($data['name_cn'])) {
            $query->where('name_cn', 'like', '%' . $data['name_cn'] . '%');
        }

        if (!empty($data['name_en'])) {
            $query->where('name_en', 'like', '%' . $data['name_en'] . '%');
        }

        if (isset($data['type'])) {
            $query->where('type', $data['type']);
        }

        if (isset($data['value_chain_id'])) {
            $query->where('value_chain_id', $data['value_chain_id']);
        }

        if (isset($data['bu_id'])) {
            $query->where('bu_id', $data['bu_id']);
        }

        if (isset($data['status'])) {
            $query->where('status', $data['status']);
        }

        // 分页
        $page = $data['page'] ?? 1;
        $pageSize = $data['page_size'] ?? 50;

        $result = $query->paginate($pageSize, ['*'], 'page', $page);

        $list = $result->items();
        $formattedList = [];

        foreach ($list as $item) {
            $formattedItem = [
                'id' => $item->id,
                'name_cn' => $item->name_cn,
                'name_en' => $item->name_en,
                'type' => $item->type,
                'type_description' => ProductCategoryModel::TYPE_DESCRIPTION_CN[$item->type] ?? '',
                'value_chain_id' => $item->value_chain_id,
                'value_chain_name' => [1 => 'A', 2 => 'B', 3 => 'C', 4 => 'Other'][$item->value_chain_id] ?? '',
                'bu_id' => $item->bu_id,
                'bu_name' => $item->bu->name ?? '',
                'memo' => $item->memo,
                'status' => $item->status,
                'status_text' => $item->status == 1 ? '启用' : '禁用',
                'created_at' => $item->created_at ? $item->created_at->format('Y-m-d H:i:s') : null,
                'updated_at' => $item->updated_at ? $item->updated_at->format('Y-m-d H:i:s') : null,
                'levels' => $item->levels->map(function ($level) {
                    return [
                        'id' => $level->id,
                        'name_cn' => $level->name_cn,
                        'name_en' => $level->name_en,
                        'status' => $level->status,
                        'status_text' => $level->status == 1 ? '启用' : '禁用'
                    ];
                })->toArray()
            ];
            $formattedList[] = $formattedItem;
        }

        return [
            'list' => $formattedList,
            'total' => $result->total(),
            'current_page' => $result->currentPage(),
            'per_page' => $result->perPage(),
            'last_page' => $result->lastPage()
        ];
    }

    /**
     * 获取产品规格列表
     * @param array $data
     * @return ListCollection
     */
    public function getSpecificationList(array $data = []): ListCollection
    {
        $query = ProductSpecificationModel::with([
            'category',
            'category.bu',
            'level',
            'achievement',
            'prices' => function (HasMany $q) {
                //只拿一条
                $q->where('status', 1)->orderByDesc('start_date');
            },
            'currentPrice',
            'subjects.subject',
            'regionValue',
            'classTypeValue',
            'teacherTypeValue',
            'pricingTypeValue'
        ])->where('id', '!=', 0)
          ->orderBy('status', 'desc')
          ->orderBy('id', 'desc');

        // 筛选条件

        //产品编号/名称
        if (!empty($data['name_cn'])) {
            $query->where(function (Builder $q) use ($data) {
                $q->where('name_cn', 'like', '%' . $data['name_cn'] . '%')->orWhere('code', 'like', '%' . $data['name_cn'] . '%');
            });
        }
        //班型
        if (!empty($data['class_type'])) {
            $query->where('class_type', $data['class_type']);
        }

        //价格模式
        if (isset($data["pricing_type"])) {
            $query->where('pricing_type', $data["pricing_type"]);
        }

        //产品类别
        if (isset($data['category_id'])) {
            $query->where('category_id', $data['category_id']);
        }

        //产品线
        if (isset($data["bu_id"])) {
            $query->where('bu_id', $data['bu_id']);
        }

        //绩效类型
        if (isset($data['achievement_id'])) {
            $query->where('achievement_id', $data['achievement_id']);
        }

        //业务分级
        if (isset($data['level_id'])) {
            $query->where('level_id', $data['level_id']);
        }

        //地区
        if (isset($data['region'])) {
            $query->where('region', $data['region']);
        }

        //科目
        if (!empty($data['subject'])) {
            $query->whereHas('subjects.subject', function (Builder $q) use ($data) {
                $q->where('name_cn', "like", "%" . $data['subject'] . "%");
            });
        }
        
        //定价方案
        if (isset($data['pricing_type'])) {
            $query->where('pricing_type', $data['pricing_type']);
        }

        //状态

        if (isset($data['status'])) {
            $query->where('status', $data['status']);
        }

        // 分页
        $page = $data['page'] ?? 1;
        $pageSize = $data['page_size'] ?? 15;

        $result = $query->paginate($pageSize, ['*'], 'page', $page);

        return ListCollection::make($result, ProductCenterListResource::class);
    }

    /**
     * 获取科目列表
     * @param array $data
     * @return array
     */
    public function getSubjectList($data = []) {
        $query = SubjectModel::query();

        // 筛选条件
        if (!empty($data['name_cn'])) {
            $query->where('name_cn', 'like', '%' . $data['name_cn'] . '%');
        }

        if (!empty($data['name_en'])) {
            $query->where('name_en', 'like', '%' . $data['name_en'] . '%');
        }

        if (isset($data['status'])) {
            $query->where('status', $data['status']);
        }

        // 分页
        $page = $data['page'] ?? 1;
        $pageSize = $data['page_size'] ?? 50;

        $result = $query->paginate($pageSize, ['*'], 'page', $page);

        $list = $result->items();
        $formattedList = [];

        foreach ($list as $item) {
            $formattedItem = [
                'id' => $item->id,
                'name_cn' => $item->name_cn,
                'name_en' => $item->name_en,
                'status' => $item->status,
                'status_text' => $item->status == 1 ? '启用' : '禁用'
            ];
            $formattedList[] = $formattedItem;
        }

        return [
            'list' => $formattedList,
            'total' => $result->total(),
            'current_page' => $result->currentPage(),
            'per_page' => $result->perPage(),
            'last_page' => $result->lastPage()
        ];
    }

    /**
     * 获取搜索参数选项
     * 基于getSpecificationList方法的筛选条件构造
     * @return array
     */
    public function getSearchParams(): array
    {
        // 获取产品类别选项
        $categories = ProductCategoryModel::where('status', 1)
            ->select('id', 'name_cn', 'type', 'bu_id')
            ->with('bu:id,name')
            ->get()
            ->map(function ($category) {
                return [
                    'key' => $category->id,
                    'value' => $category->name_cn,
                    'type' => $category->type,
                    'type_name' => ProductCategoryModel::TYPE_DESCRIPTION_CN[$category->type] ?? '',
                    'bu_id' => $category->bu_id,
                    'bu_name' => $category->bu->name ?? ''
                ];
            })
            ->toArray();

        // 获取产品线选项
        $buList = \App\Models\PublicModels\BuModel::where('is_deleted', 0)
            ->select('id', 'name')
            ->get()
            ->map(function ($bu) {
                return [
                    'key' => $bu->id,
                    'value' => $bu->name
                ];
            })
            ->toArray();

        // 获取绩效类型选项
        $achievements = \App\Models\PublicModels\Product\AchievementModel::select('id', 'name')
            ->get()
            ->map(function ($achievement) {
                return [
                    'key' => $achievement->id,
                    'value' => $achievement->name
                ];
            })
            ->toArray();

        // 获取业务分级选项
        $levels = \App\Models\PublicModels\Product\ProductLevelModel::where('status', 1)
            ->select('id', 'category_id', 'name_cn')
            ->get()
            ->map(function ($level) {
                return [
                    'key' => $level->id,
                    'value' => $level->name_cn,
                    'category_id' => $level->category_id
                ];
            })
            ->toArray();

        // 获取定价因素值选项
        $pricingFactorValues = PricingFactorValueModel::where('status', 1)
            ->select('id', 'pricing_factor_id', 'name_cn')
            ->get();

        // 按定价因素类型分组
        $regions = $pricingFactorValues
            ->where('pricing_factor_id', PricingFactorValueModel::REGION)
            ->map(function ($item) {
                return [
                    'key' => $item->id,
                    'value' => $item->name_cn
                ];
            })
            ->values()
            ->toArray();

        $classTypes = $pricingFactorValues
            ->where('pricing_factor_id', PricingFactorValueModel::CLASS_TYPE)
            ->map(function ($item) {
                return [
                    'key' => $item->id,
                    'value' => $item->name_cn
                ];
            })
            ->values()
            ->toArray();

        $teacherTypes = $pricingFactorValues
            ->where('pricing_factor_id', PricingFactorValueModel::TEACHER_TYPE)
            ->map(function ($item) {
                return [
                    'key' => $item->id,
                    'value' => $item->name_cn
                ];
            })
            ->values()
            ->toArray();

        $pricingTypes = $pricingFactorValues
            ->where('pricing_factor_id', PricingFactorValueModel::PRICING_TYPE)
            ->map(function ($item) {
                return [
                    'key' => $item->id,
                    'value' => $item->name_cn
                ];
            })
            ->values()
            ->toArray();

        // 获取科目选项
        $subjects = SubjectModel::where('status', 1)
            ->select('id', 'name_cn')
            ->get()
            ->map(function ($subject) {
                return [
                    'id' => $subject->id,
                    'name' => $subject->name_cn
                ];
            })
            ->toArray();

        // 状态选项
        $statusOptions = [
            ['id' => 1, 'name' => '启用'],
            ['id' => 0, 'name' => '禁用']
        ];

        // 产品类型选项
        $typeOptions = [];
        foreach (ProductCategoryModel::TYPE_DESCRIPTION_CN as $key => $value) {
            $typeOptions[] = [
                'id' => $key,
                'name' => $value
            ];
        }

        return [
            'categories' => $categories,           // 产品类别
            'bu_list' => $buList,                 // 产品线
            'achievements' => $achievements,       // 绩效类型
            'levels' => $levels,                  // 业务分级
            'regions' => $regions,                // 地区
            'class_types' => $classTypes,         // 班型
            'teacher_types' => $teacherTypes,     // 教师类型
            'pricing_types' => $pricingTypes,     // 定价方案
            'subjects' => $subjects,              // 科目
            'status_options' => $statusOptions,   // 状态选项
            'type_options' => $typeOptions,       // 产品类型选项
        ];
    }
}
