<?php

namespace App\Http\Service\PublicService;

class MenuService
{
    /**
     * 生成菜单树
     * @param $menus
     * @param int $parent_id
     * @return array
     */
    public function makeMenuTree($menus, int $parent_id = 0): array
    {
        $branch = [];
        foreach ($menus as $menu) {
            if ($menu->parent_id == $parent_id) {
                $menuArray = [
                    'id' => $menu->id,
                    'name_cn' => $menu->name_cn,
                    'name_en' => $menu->name_en,
                    'url' => $menu->url,
                    'icon' => $menu->icon,
                    'sort' => $menu->sort,
                    'type' => $menu->type,
                    'children' => [],
                ];
                $children = $this->makeMenuTree($menus, $menu->id);
                if ($children) {
                    $menuArray['children'] = $children;
                }
                $branch[] = $menuArray;
            }
        }
        usort($branch, function ($a, $b) {
            return $a['sort'] - $b['sort'];
        });
        return $branch;
    }
}
