<?php

namespace App\Http\Service\PublicService;

use App\Models\PublicModels\UploadFileModel;
use Exception;
use Illuminate\Support\Facades\Storage;

class UploadService
{
    const array CONFIG = [
        UploadFileModel::RESOURCE_TYPE_SCORE => [
            'dir' => 'score',
            'ext' => ['pdf'],
            'size' => 10 * 1024 * 1024,
        ]
    ];

    /**
     * @throws Exception
     */
    public function upload($file, $type)
    {
        #文件验证
        $this->_check($file, $type);
        #落盘
        $strFilePath = $this->_saveFile($file, $type);
        #入库
        return UploadFileModel::create([
            'resource_type' => $type,
            'file_name' => $file->getClientOriginalName(),
            'file_format' => $file->getClientMimeType(),
            'file_path' => $strFilePath,
        ]);
    }

    /**
     * @throws Exception
     */
    private function _check($file, $type): void
    {
        if (!isset(self::CONFIG[$type])) {
            throw new Exception('非法参数');
        }
        if (!is_file($file)) {
            throw new Exception('未上传文件');
        }
        if (!$file->isValid()) {
            throw new Exception('文件校验失败');
        }

        $arrAllowExt = self::CONFIG[$type]['ext'];
        $iAllowSize = self::CONFIG[$type]['size'];

        if (!in_array($file->getClientOriginalExtension(), $arrAllowExt)) {
            throw new Exception('文件类型不符合');
        }

        if ($file->getSize() >= $iAllowSize) {
            throw new Exception('文件尺寸过大');
        }
    }

    private function _saveFile($file, $type): false|string
    {
        $dir = self::CONFIG[$type]['dir'];
        $dir = $dir . DIRECTORY_SEPARATOR . date('Y-m-d') . DIRECTORY_SEPARATOR;
        Storage::disk('public')->makeDirectory($dir);
        return Storage::disk('public')->putFile($dir, $file);
    }
}
