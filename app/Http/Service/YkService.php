<?php

namespace App\Http\Service;

use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;

class YkService
{
    const API = '/api/wework/index';


    /**
     * @param $key
     * @return array
     */
    public function getYkData($key): array
    {
        $client = new Client();
        $headers = [
            'Content-Type' => 'application/json'
        ];
        $body = '{"action": "' . $key . '"}';
        $request = new Request('POST', env('YK_BASE_URL', 'https://yk.xkt.com') . self::API, $headers, $body);
        $res = $client->sendAsync($request)->wait();
        $obj = json_decode($res->getBody(), true);
        if (isset($obj['code']) && $obj['code'] != 0) {
            return ['success' => false, 'data' => [], 'message' => $obj['message'], 'code' => $obj['code']];
        }
        return ['success' => true, 'data' => $obj['data'] ?? []];
    }
}
