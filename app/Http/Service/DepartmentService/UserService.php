<?php

namespace App\Http\Service\DepartmentService;

use App\Http\Resources\User\DetailResource;
use App\Http\Service\PublicService\MenuService;
use App\Models\DepartmentModels\DeptModel;
use App\Models\DepartmentModels\SharePrivilegeModel;
use App\Models\DepartmentModels\UserModel;
use App\Models\PublicModels\MenuModel;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;

class UserService
{
    /**
     * 树状 num 发生器
     *
     * @return bool
     */
    public function generateUserNum(): bool
    {
        UserModel::query()->update(['user_num' => 0]);// 重置
        $allUsers = UserModel::all();
        $parentChildMap = [];
        foreach ($allUsers as $user) {
            $parentChildMap[$user->ehr_puid][] = [
                'id' => $user->id,
                'ehr_puid' => $user->ehr_puid,
                'ehr_uid' => $user->ehr_uid,
            ];
        }
        $resultMap = $this->generateChildNum($parentChildMap, 0, 1001, []);
        $index = 1;
        $end = count($resultMap);
        $ids = [];
        $cases = "";
        foreach ($resultMap as $id => $num) {
            $cases .= "WHEN id = {$id} THEN '{$num}'";
            $ids[] = $id;
            if ($index % 1000 === 0 || $index === $end) {
                $ids = implode(',', $ids);
                DB::update("UPDATE crm_users SET user_num = CASE {$cases} END WHERE id IN ({$ids})");
                $cases = "";
                $ids = [];
            }
            $index++;
        }
        return true;
    }


    /**
     * 递归生成用户 num
     *
     * @param array $parentChildMap
     * @param $parentUid
     * @param $currentNum
     * @param $resultMap
     * @return mixed
     */
    private function generateChildNum(array $parentChildMap, $parentUid, $currentNum, $resultMap): mixed
    {
        $targetUsers = $parentChildMap[$parentUid] ?? [];
        if (empty($targetUsers)) {
            return $resultMap;
        }
        $index = 1001;
        foreach ($targetUsers as $user) {
            $num = $currentNum . $index;
            $resultMap[$user['id']] = $num;
            $resultMap = $this->generateChildNum($parentChildMap, $user['ehr_uid'], $num, $resultMap);
            $index++;
        }
        return $resultMap;
    }


    /**
     * 更新 ehr_puid 为 0 且 user_status 为 0 的用户
     * 将他们的 ehr_puid 更新为 ehr_puid 为 0 且 user_status 为 1 的唯一用户的 ehr_uid
     *
     * @return bool
     */
    public function updateHistoryUsersEhrPuid(): bool
    {
        // 查找 ehr_puid 为 0 且 user_status 为 1 的唯一用户
        $targetUser = UserModel::where('ehr_puid', 0)
            ->where('user_status', 1)
            ->first();
        if (!$targetUser) {
            return false; // 没有找到目标用户
        }
        // 更新所有 ehr_puid 为 0 或 null
        $updatedCount = UserModel::where(function ($query) {
            $query->where('ehr_puid', 0)
                ->orWhereNull('ehr_puid');
        })
            ->where('id', "!=", $targetUser->id) // 排除目标用户自身
            ->update(['ehr_puid' => $targetUser->ehr_uid]);

        return $updatedCount > 0;
    }


    /**
     * 使用ua数据同步用户信息
     * @param $uaData
     * @return void
     */
    public function syncUser($uaData): void
    {
        Log::channel('ua_sync')->info(json_encode($uaData));
        $userCode = $uaData['user_code'];
        $userStatus = $uaData['user_status'];

        //获取yk用户信息
        $targetUser = null;
        $ykService = new YkService();
        if ($userStatus == 1) {
            //获取ehr在职用户信息
            $res = $ykService->getYkData('GetEhrUsers');
            if (!$res['success']) {
                Log::channel('ua_sync')->error('获取yk用户信息(工号:' . $userCode . ')失败' . $res['message']);
                return;
            }
            $datas = $res['data'];
            foreach ($datas as $data) {
                if ($data['userid'] == $userCode) {
                    $targetUser = $data;
                    break;
                }
            }
            //获取企业微信在职用户信息
            $res = $ykService->getYkData('GetUsers');
            if (!$res['success']) {
                Log::channel('ua_sync')->error('获取yk用户信息(工号:' . $userCode . ')失败' . $res['message']);
                return;
            }
            $datas = $res['data'];
            foreach ($datas as $data) {
                if ($data['userid'] == $userCode) {
                    $targetUser['oa_departid'] = $data['oa_departid'];
                    $targetUser['oaid'] = $data['oaid'];
                    $targetUser['parentoaid'] = $data['parentoaid'];
                    break;
                }
            }
        } else {
            //获取离职用户信息
            $res = $ykService->getYkData('GetResignUser');
            if (!$res['success']) {
                Log::channel('ua_sync')->error('获取yk用户信息(工号:' . $userCode . ')失败' . $res['message']);
                return;
            }
            $datas = $res['data'];
            foreach ($datas as $data) {
                if ($data['userid'] == $userCode) {
                    $targetUser = $data;
                    break;
                }
            }
        }

        if (!$targetUser) {
            Log::channel('ua_sync')->error('获取yk用户信息(工号:' . $userCode . ')失败');
            return;
        }

        //获取所在部门
        $targetDept = DeptModel::where('ehr_id', $targetUser['ehr_departid'])->first();
        if (!$targetDept) {
            Log::channel('ua_sync')->error('获取所在团队异常(工号:' . $userCode . ')，部门id:' . $targetUser['ehr_departid']);
            return;
        }

        if ($userStatus == 1) {
            //在职用户采用更新或新增处理
            $objUser = UserModel::updateOrCreate([
                'user_code' => $userCode,
            ], [
                'name' => $uaData['name'],
                'ua_id' => $uaData['ua_id'],
                'position' => $targetUser['position'],
                'oa_id' => $uaData['oa_id'],
                'parent_oa_id' => $uaData['parent_oa_id'],
                'reset_day' => $targetUser['offday'],
                'oa_dept_id' => $targetUser['oa_departid'],
                'ehr_depart_id' => $targetUser['ehr_departid'],
                'ehr_uid' => $targetUser['ehr_uid'],
                'ehr_puid' => $targetUser['ehr_puid'],
                'user_status' => $userStatus,
                'campus' => "",
                'region' => "",
                'employ_type' => $uaData['employ_type'],
                'staff_mail' => $uaData['staff_mail'],
                'wx_avatar' => $targetUser['wx_avatar'],
                'crm_depart_id' => $targetDept->id,
            ]);
        } else {
            #判断用户是否存在
            $objUser = UserModel::where('user_code', $userCode)->first();
            if (!$objUser) {
                Log::channel('ua_sync')->error('未找到有效用户(工号:' . $userCode . ')失败');
                return;
            }
            $objUser->update([
                'position' => $targetUser['position'],
                'parent_oa_id' => $uaData['parent_oa_id'],
                'reset_day' => $targetUser['offday'],
                'oa_dept_id' => $targetUser['oa_departid'],
                'ehr_depart_id' => $targetUser['ehr_departid'],
                'ehr_puid' => $targetUser['ehr_puid'],
                'user_status' => $userStatus,
                'employ_type' => $uaData['employ_type'],
                'wx_avatar' => $targetUser['wx_avatar'],
                'crm_depart_id' => $targetDept->id,
            ]);
        }

        // 更新用户角色
        $arrRoleArrange = Role::whereIn('use_name', $uaData['role']['crm3']['all'])->get();
        foreach ($arrRoleArrange as $local) {
            $objUser->removeRole($local);
        }
        foreach ($uaData['role']['crm3']['now'] as $newRole) {
            $arrRoleArrange->where('use_name', $newRole)->isNotEmpty()
            && $objUser->assignRole($arrRoleArrange->where('use_name', $newRole)->first()->name);
        }
    }

    /**
     * 批量获取额外数据权限
     * @param $users
     * @return array
     */
    public function getBatchExtraPrivileges($users): array
    {
        $owners = [];
        $servers = [];
        $consultants = [];
        if (empty($users)) {
            return [
                'owners' => $owners,
                'servers' => $servers,
                'consultants' => $consultants,
            ];
        }
        $userIds = [];
        $departIds = [];
        foreach ($users as $user) {
            $userIds[] = $user->id;
            $departIds[] = $user->crm_depart_id;
        }
        $userIds = array_unique($userIds);
        $departIds = array_unique($departIds);

        $privileges = SharePrivilegeModel::where('status', SharePrivilegeModel::STATUS_ENABLE)
            ->where(function (Builder $query) use ($userIds, $departIds) {
                $query->where(function (Builder $query) use ($userIds) {
                    // 1人-人
                    // 2人及下属-人
                    // 3部门-人
                    // 4部门及下属部门-人
                    $query->whereJsonContains('to_ids', $userIds)
                        ->whereIn('obj_type', [
                            SharePrivilegeModel::OBJ_TYPE_PERSON_TO_PERSON,
                            SharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON,
                            SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_TO_PERSON,
                            SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_PERSON
                        ]);
                })
                    ->orWhere(function (Builder $query) use ($departIds) {
                        // 5人-部门
                        // 6人及下属-部门
                        // 7部门-部门
                        // 8部门及下属部门-部门
                        $query->whereJsonContains('to_ids', $departIds)
                            ->whereIn('obj_type', [
                                SharePrivilegeModel::OBJ_TYPE_PERSON_TO_DEPARTMENT,
                                SharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT,
                                SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_TO_DEPARTMENT,
                                SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_DEPARTMENT,
                            ]);
                    });
            })
            ->where('share_type', SharePrivilegeModel::SHARE_TYPE_EXTRA)
            ->where('start_date', '<=', date('Y-m-d'))
            ->where('end_date', '>=', date('Y-m-d'))
            ->get();

        foreach ($privileges as $item) {
            $roles = $item->roles;
            if (in_array($item->obj_type, [
                SharePrivilegeModel::OBJ_TYPE_PERSON_TO_PERSON,
                SharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON,
                SharePrivilegeModel::OBJ_TYPE_PERSON_TO_DEPARTMENT,
                SharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT,
            ])) {
                // 授权源头是人
                if (in_array(SharePrivilegeModel::SHARE_ROLE_OWNER, $roles)) $owners = array_merge($owners, $item->from_ids);
                if (in_array(SharePrivilegeModel::SHARE_ROLE_SERVER, $roles)) $servers = array_merge($servers, $item->from_ids);
                if (in_array(SharePrivilegeModel::SHARE_ROLE_CONSULTANT, $roles)) $consultants = array_merge($consultants, $item->from_ids);
                if ($item->obj_type == SharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON || $item->obj_type == SharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT) {
                    // 只获取用户编号
                    $userNums = UserModel::whereIn('id', $item->from_ids)->pluck('user_num');
                    // 只有在找到了 user_num 时才需要查询下属
                    if ($userNums->isNotEmpty()) {
                        // 使用 where closure 构建查询，避免多个 orWhere 导致的性能问题，并只获取ID
                        $subordinateIds = UserModel::where(function ($query) use ($userNums) {
                            foreach ($userNums as $num) {
                                $query->orWhere('user_num', 'like', $num . '%');
                            }
                        })->pluck('id'); // 直接获取所有符合条件的下属ID集合
                        if (in_array(SharePrivilegeModel::SHARE_ROLE_OWNER, $roles)) $owners = array_merge($owners, $subordinateIds->toArray());
                        if (in_array(SharePrivilegeModel::SHARE_ROLE_SERVER, $roles)) $servers = array_merge($servers, $subordinateIds->toArray());
                        if (in_array(SharePrivilegeModel::SHARE_ROLE_CONSULTANT, $roles)) $consultants = array_merge($consultants, $subordinateIds->toArray());
                    }
                }
            } else {
                // 授权源头是部门
                $targetUsers = UserModel::whereIn('crm_depart_id', $item->from_ids)->get();
                if (in_array(SharePrivilegeModel::SHARE_ROLE_OWNER, $roles)) $owners = array_merge($owners, array_keys($targetUsers));
                if (in_array(SharePrivilegeModel::SHARE_ROLE_SERVER, $roles)) $servers = array_merge($servers, array_keys($targetUsers));
                if (in_array(SharePrivilegeModel::SHARE_ROLE_CONSULTANT, $roles)) $consultants = array_merge($consultants, array_keys($targetUsers));
                if ($item->obj_type == SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_PERSON ||
                    $item->obj_type == SharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_DEPARTMENT) {
                    // 附带子部门
                    $deptNums = DeptModel::whereIn('id', $item->from_ids)->pluck('dept_num');
                    if ($deptNums->isNotEmpty()) {
                        $subDeptIds = DeptModel::where(function ($query) use ($deptNums) {
                            foreach ($deptNums as $num) {
                                $query->orWhere('dept_num', 'like', $num . '%');
                            }
                        })->pluck('id');
                        if ($subDeptIds->isNotEmpty()) {
                            $deptUsers = UserModel::whereIn('crm_depart_id', $subDeptIds->toArray())->pluck('id');
                            if (in_array(SharePrivilegeModel::SHARE_ROLE_OWNER, $roles)) $owners = array_merge($owners, $deptUsers->toArray());
                            if (in_array(SharePrivilegeModel::SHARE_ROLE_SERVER, $roles)) $servers = array_merge($servers, $deptUsers->toArray());
                            if (in_array(SharePrivilegeModel::SHARE_ROLE_CONSULTANT, $roles)) $consultants = array_merge($consultants, $deptUsers->toArray());
                        }
                    }
                }
            }
        }
        return [
            'owners' => array_unique($owners),
            'servers' => array_unique($servers),
            'consultants' => array_unique($consultants),
        ];
    }


    /**
     * 获取当前用户详情
     * @return DetailResource
     */
    public function getCurrentUserDetail(): DetailResource
    {
        $user = auth()->user();
        $user->getUserMenus();
        return DetailResource::make($user);
    }
}
