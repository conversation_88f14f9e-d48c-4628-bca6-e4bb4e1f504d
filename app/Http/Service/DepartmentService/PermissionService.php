<?php

namespace App\Http\Service\DepartmentService;

use App\Models\DepartmentModels\PermissionModel;
use App\Policies\GatePermissionPolicy;
use Illuminate\Support\Facades\Gate;

class PermissionService
{
    public function GatePermissions(): array
    {
        $argv = request()->server()['argv'] ?? '';
        if ($argv) {
            return [];
        }
        $permission = PermissionModel::where("type", "api")->get();
        $function = [];
        foreach ($permission as $value) {
            $slug = $value->slug;
            if (!$slug) {
                continue;
            }
            $slug = explode('-', $slug);
            $slug = collect($slug);
            $slug = $slug->map(function ($value, $key) {
                if ($key != 0) {
                    $value = ucfirst($value);
                }
                return $value;
            });
            $slug = $slug->implode('');
            $function[$value->slug] = $slug;
        }
        return $function;
    }

    public function registerGatePermissions() {
        Gate::before(function ($user, $ability) {
            //特殊用户处理
            if ($user->is_admin) {
                return true;
            }
            return null;
        });
        $gatePermissions = $this->GatePermissions();
        foreach ($gatePermissions as $ability => $callback) {
            Gate::define($ability, [GatePermissionPolicy::class, $callback]);
        }
    }
}
