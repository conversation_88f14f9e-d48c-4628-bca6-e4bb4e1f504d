<?php

namespace App\Http\Resources\Customer;

use App\Models\EntityModels\Customer\CustomerRelationModel;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = auth()->user();
        $userIds = $user->all_employees ?? [$user->id];
        $sharePrivileges = $user->shared_privileges ?? [];
        $extraPrivileges = $user->extra_privileges ?? [];

        $sharePrivilege = [];
        $extraPrivilege = [];
        $privilege = [];
        $relations = [];
        foreach ($this->activeRelations as $relation) {
            $relations[] = $relation->user->name;
            switch ($relation->type) {
                case CustomerRelationModel::TYPE_OWNER:
                    if (in_array($relation->user->id, $userIds)) {
                        $privilege[CustomerRelationModel::TYPE_OWNER] = '跟进人';
                    }
                    if (in_array($relation->user->id, $sharePrivileges)) {
                        $sharePrivilege[CustomerRelationModel::TYPE_OWNER] = '跟进人';
                    }
                    if (in_array($relation->user->id, $extraPrivileges['owners'])) {
                        $extraPrivilege[CustomerRelationModel::TYPE_OWNER] = '跟进人';
                    }
                    break;
                case CustomerRelationModel::TYPE_SERVER:
                    if (in_array($relation->user->id, $userIds)) {
                        $privilege[CustomerRelationModel::TYPE_SERVER] = '维护人';
                    }
                    if (in_array($relation->user->id, $sharePrivileges)) {
                        $sharePrivilege[CustomerRelationModel::TYPE_SERVER] = '维护人';
                    }
                    if (in_array($relation->user->id, $extraPrivileges['servers'])) {
                        $extraPrivilege[CustomerRelationModel::TYPE_SERVER] = '维护人';
                    }
                    break;
                case CustomerRelationModel::TYPE_CONSULTANT:
                    if (in_array($relation->user->id, $userIds)) {
                        $privilege[CustomerRelationModel::TYPE_CONSULTANT] = '顾问';
                    }
                    if (in_array($relation->user->id, $sharePrivileges)) {
                        $sharePrivilege[CustomerRelationModel::TYPE_CONSULTANT] = '顾问';
                    }
                    if (in_array($relation->user->id, $extraPrivileges['consultants'])) {
                        $extraPrivilege[CustomerRelationModel::TYPE_CONSULTANT] = '顾问';
                    }
                    break;
                default:
                    break;
            }
        }
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'english_name' => $this->english_name,
            'gender' => trans('customer.' . ($this->gender == 0 ? 'male' : 'female')),
            'region_code' => $this->currentSchool->region_code,
            'school_name_cn' => $this->currentSchool->name_cn,
            'school_name_en' => $this->currentSchool->name_en,
            'course_system' => $this->currentSchool->getCourseSystems(),
            'grade' => $this->currentGrade ? $this->currentGrade->grade_name : 'N/A',
            'training_status' => $this->training_status,
            'study_status' => $this->study_status,
            'abroad_intention' => $this->abroad_intention,
            'relations' => array_unique($relations),
            'privilege' => $privilege,
            'share_privilege' => $sharePrivilege,
            'extra_privilege' => $extraPrivilege,
        ];
    }
}
