<?php

namespace App\Http\Resources\Customer;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ListCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'list' => ListResource::collection($this),
            'total' => $this->total(),
            'page' => $this->currentPage(),
            'size' => $this->perPage(),
            'totalPage' => $this->lastPage(),
        ];
    }
}
