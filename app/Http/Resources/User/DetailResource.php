<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'user_code' => $this->user_code,
            'ua_id' => $this->ua_id,
            'is_admin' => $this->is_admin,
            'position' => $this->position,
            'oa_id' => $this->oa_id,
            'reset_day' => $this->reset_day,
            'staff_mail' => $this->staff_mail,
            'wx_avatar' => $this->wx_avatar,
            'crm_depart_id' => $this->crm_depart_id,
            'menus' => $this->menus,
        ];
    }
}
