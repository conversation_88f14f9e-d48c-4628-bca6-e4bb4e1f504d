<?php

namespace App\Http\Resources\ProductCenter;

use App\Models\PublicModels\Product\PricingFactorValueModel;
use App\Models\PublicModels\Product\ProductCategoryModel;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

class ProductCenterListResource extends JsonResource
{
    public function toArray(Request $request): array|JsonSerializable|Arrayable
    {
        $item = $this;
        return [
            'id' => $item->id,
            'code' => $item->code,
            'name_cn' => $item->name_cn,//产品规格名称
            'name_en' => $item->name_en,
            'category_id' => $item->category_id,
            'category_name_cn' => $item->category->name_cn ?? '', //产品类别
            'category_type' => $item->category->type ?? null,
            'category_type_description' => isset($item->category->type) ? ProductCategoryModel::TYPE_DESCRIPTION_CN[$item->category->type] : '',
            'category_bu' => optional($item->category)->bu_id,
            'category_bu_text' => optional(optional($item->category)->bu)->name,
            'custom_price' => $item->custom_price,
            'custom_price_text' => $item->custom_price == 1 ? '是' : '否',
            'level_id' => $item->level_id,
            'level_name_cn' => $item->level->name_cn ?? '', //业务分级
            'achievement_id' => $item->achievement_id,
            'achievement_name' => $item->achievement->name ?? '', //绩效类型
            'currency' => $item->currency,
            'currency_text' => $item->currency == 1 ? '$' : '¥',
            'current_price' => optional($item->currentPrice)->price,
            "current_price_collection" => PriceResource::collection($item->prices),
            'price_type' => $item->price_type, //价格模式
            'price_type_text' => $item->price_type == 0 ? '单价模式' : '总价模式',
            'static_quantity' => $item->static_quantity, //数量
            'status' => $item->status,
            'status_text' => $item->status == 1 ? '启用' : '禁用',
            'memo' => $item->memo,
            'created_at' => $item->created_at ? $item->created_at->format('Y-m-d H:i:s') : null,
            'updated_at' => $item->updated_at ? $item->updated_at->format('Y-m-d H:i:s') : null,
            'region_text' => optional($item->regionValue)->name_cn,
            "region" => $item->region,
            "class_type" => $item->class_type,
            "class_type_text" => optional($item->classTypeValue)->name_cn,
            "teacher_type" => $item->teacher_type,
            "teacher_type_text" => optional($item->teacherTypeValue)->name_cn,
            "pricing_type" => $item->pricing_type,
            "pricing_type_text" => optional($item->pricingTypeValue)->name_cn, //定价方案
            "subjects_values" => $item->subjects->pluck("subject.name_cn")->unique()->implode("、"), //科目信息
        ];
    }
}
