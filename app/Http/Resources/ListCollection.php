<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

/**
 * 通用列表
 * @method total() 总数
 * @method currentPage() 当前页
 * @method perPage() 页数
 * @method lastPage() 总页
 */
class ListCollection extends ResourceCollection
{
    /**
     * @param $resource
     * @param string $collects | 指定的item资源
     */
    public function __construct($resource, string $collects = "")
    {
        parent::__construct($resource);
        $this->collects = $collects;
    }

    public function toArray(Request $request): array
    {
        return [
            'list' => $this->collects()::collection($this),
            'total' => $this->total(),
            'page' => $this->currentPage(),
            'page_size' => $this->perPage(),
            'total_page' => $this->lastPage(),
        ];
    }

}
