<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tag', function (Blueprint $table) {
            $table->id();
            $table->string('name_cn')->comment('标签中文名称');
            $table->string('name_en')->nullable()->comment('标签英文名称');
            $table->integer('resource_type')->comment('资源类型 1:产品 2:客户');
            $table->string('function_name')->nullable()->comment('对应服务方法名');
            $table->json('function_params')->nullable()->comment('对应服务方法参数');
            $table->string('description')->nullable()->comment('描述');
            $table->integer('status')->default(1)->comment('状态 0:禁用 1:启用');
            $table->integer('created_by')->comment('创建人');
            $table->integer('updated_by')->comment('更新人');
            $table->timestamps();
            $table->comment('系统级标签表');
        });

        Schema::create('tag_relation', function (Blueprint $table) {
            $table->id();
            $table->integer('tag_id')->comment('标签ID');
            $table->integer('resource_id')->comment('资源ID');
            $table->integer('resource_type')->comment('资源类型');
            $table->integer('created_by')->comment('创建人');
            $table->integer('updated_by')->comment('更新人');
            $table->softDeletesDatetime();
            $table->timestamps();
            $table->comment('系统级标签关联表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tag');
        Schema::dropIfExists('tag_relation');
    }
};
