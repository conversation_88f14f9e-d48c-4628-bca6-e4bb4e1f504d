<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer', function (Blueprint $table) {
            //基础信息字段
            $table->id()->comment('主键');
            $table->string('ua_id', 255)->nullable()->comment('uaid');
            $table->string('ua_mobile', 20)->nullable()->comment('UA唯一认证电话');
            $table->integer('wx_id')->nullable()->comment('微信识别ID');
            $table->string('code', 20)->comment('学号');
            $table->string('name', 100)->comment('姓名');
            $table->string('english_name', 100)->nullable()->comment('英文名');
            $table->string('is_from_lead', 3)->nullable()->comment('是否由线索转换而来');
            $table->integer('gender')->nullable()->comment('性别');
            $table->integer('customer_stage')->default(2)->comment('客户阶段 1线索 2客户');
            $table->text('description')->nullable()->comment('描述');
            //各类业务状态
            $table->string('training_status', 20)->nullable()->comment('培训状态');
            $table->string('study_status', 20)->nullable()->comment('升学状态');
            $table->string('follow_up_status', 20)->nullable()->comment('线索跟进状态');
            //来源信息
            $table->string('source', 200)->nullable()->comment('来源');
            $table->string('source_detail', 255)->nullable()->comment('来源详情');
            $table->string('source_memo', 255)->nullable()->comment('来源备注');
            $table->string('phone', 100)->nullable()->comment('电话');
            $table->string('phone_owner', 20)->nullable()->comment('电话归属人');
            $table->string('other_contact', 255)->nullable()->comment('其他联系方式类型');
            $table->string('other_contact_detail', 255)->nullable()->comment('其他联系方式详情');
            $table->integer('school')->nullable()->comment('当前学校');
            $table->string('grade', 10)->nullable()->comment('当前年级');
            //线索特有信息
            $table->integer('target_campus')->nullable()->comment('意向校区');
            $table->string('consulting_course')->nullable()->comment('咨询课程');
            //学员特有字段
            $table->string('school_year', 20)->nullable()->comment('入学年份');
            //留学相关信息
            $table->string('is_intention', 10)->nullable()->comment('是否意向国际学校');
            $table->string('course_system', 100)->nullable()->comment('在读课程体系');
            $table->string('target_school', 100)->nullable()->comment('目标学校排名');
            $table->string('abroad_intention', 100)->nullable()->comment('升学意向');
            $table->string('major_intention', 100)->nullable()->comment('意向专业');
            $table->string('is_in_xkt', 100)->nullable()->comment('是否新课堂申请');
            $table->string('other_institution', 255)->nullable()->comment('其他机构');
            $table->string('apply_year', 20)->nullable()->comment('申请入读年份');
            $table->string('apply_season', 20)->nullable()->comment('申请季');
            $table->string('lose_reason', 255)->nullable()->comment('流失原因');
            //冗余字段
            $table->decimal('total_usd', 10, 2)->comment('累计缴纳美元额度');
            $table->decimal('total_rmb', 10, 2)->comment('累计缴纳人民币额度');
            //权限状态信息
            $table->integer('created_by')->comment('创建人');
            $table->integer('updated_by')->comment('更新人');
            $table->softDeletesDatetime();
            $table->timestamps();
            $table->comment('客户主表');
        });

        Schema::create('customer_relation', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('customer_id')->comment('客户id');
            $table->unsignedInteger('user_id')->comment('用户id');
            $table->dateTime('start_time')->nullable()->comment('开始时间');
            $table->dateTime('end_time')->nullable()->comment('结束时间');
            $table->text('memo')->nullable()->comment('备注');
            $table->integer('type')->nullable()->comment('1跟进人 2维护人 3顾问');
            $table->integer('status')->nullable()->comment('常规状态 0无效 1有效');
            $table->integer('created_by')->comment('创建人');
            $table->integer('updated_by')->comment('更新人');
            $table->timestamps();
            $table->index('user_id');
            $table->index('customer_id');
            $table->comment('客户用户关系表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer');
        Schema::dropIfExists('customer_relation');
    }
};
