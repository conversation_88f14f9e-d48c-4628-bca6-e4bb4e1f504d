<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('customer_communication', function (Blueprint $table) {
            $table->id();
            $table->integer('customer_id')->comment('所属学员id');
            $table->index('customer_id');
            $table->text('content')->nullable()->comment('内容');
            $table->integer('type')->comment('沟通类型 1销售沟通 2服务沟通 3售后沟通 4系统事件');
            $table->dateTime('time')->nullable()->comment('事件时间');
            $table->tinyInteger('if_intention')->nullable()->comment('是否有签约意向');
            $table->integer('bu')->nullable()->comment('预计成交产品线');
            $table->decimal('amount', 10, 2)->nullable()->comment('预计成交金额');
            $table->tinyInteger('currency')->nullable()->comment('货币单位, 1美金 2人民币');
            $table->integer('status')->nullable()->comment('状态 0无效 1有效');
            $table->integer('create_by')->nullable()->comment('创建人');
            $table->integer('update_by')->nullable()->comment('编辑人');
            $table->timestamps();
            $table->comment('客户沟通记录/轨迹');
        });
        Schema::create('communication_tag', function (Blueprint $table) {
            $table->id();
            $table->integer('type')->nullable()->comment('沟通类型 1销售沟通 2服务沟通 3售后沟通');
            $table->string('name', 255)->comment('标签名');
            $table->timestamps();
            $table->comment('沟通标签');
        });
        Schema::create('customer_communication_tags', function (Blueprint $table) {
            $table->integer('com_id')->comment('沟通记录id');
            $table->integer('tag_id')->comment('标签id');
            $table->timestamps();
            $table->comment('沟通标签关联');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_communication');
        Schema::dropIfExists('communication_tag');
        Schema::dropIfExists('customer_communication_tags');
    }
};
