<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('instance_quantity', function (Blueprint $table) {
            $table->comment('产品实例&数量关系');
            $table->id('id');
            $table->integer('instance_id')->nullable()->comment('班级id');
            $table->integer('subject_id')->nullable()->comment('科目id');
            $table->integer('staff_id')->nullable()->comment('员工id');
            $table->decimal('quantity', 8, 2)->nullable()->comment('nct+ct数量');
            $table->decimal('nct_quantity', 8, 2)->nullable()->comment('nct数量');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('instance_quantity');
    }
};
