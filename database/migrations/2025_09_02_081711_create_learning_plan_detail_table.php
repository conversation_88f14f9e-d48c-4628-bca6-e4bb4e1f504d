<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('learning_plan_detail', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('learning_plan_id')->comment('学习计划ID')->index();
            $table->string('period')->comment('规划时间');
            $table->text('arrangement')->comment('课程安排');
            $table->text('target')->comment('目标');
            $table->text('memo')->comment('其他或备注');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('learning_plan_detail');
    }
};
