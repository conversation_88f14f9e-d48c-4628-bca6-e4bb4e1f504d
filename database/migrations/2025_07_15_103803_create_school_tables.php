<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('grade', function (Blueprint $table) {
            $table->id();
            $table->string('grade_name')->comment('年级名称');
            $table->string('old_id')->comment('原来的id');
            $table->comment('年级表');
        });

        // 课程体系
        Schema::create('course_system', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('课程体系');
            $table->comment('课程体系');
            $table->integer('is_deleted')->default(0)->comment('是否删除');
        });

        Schema::create('region', function (Blueprint $table) {
            $table->id();
            $table->string('name_cn')->comment('中文名');
            $table->string('name_en')->nullable()->comment('英文名');
            $table->integer('parent_id')->default(0)->comment('父id');
            $table->integer('depth')->default(0)->comment('深度 0国家 1省 2市 3区');
            $table->comment('地区表');
        });

        Schema::create('school', function (Blueprint $table) {
            $table->id();
            $table->string('name_cn')->comment('中文名');
            $table->string('name_en')->nullable()->comment('英文名');
            $table->integer('country_id')->comment('国家id');
            $table->integer('province_id')->comment('省id');
            $table->integer('city_id')->comment('城市id');
            $table->json('scope')->nullable()->comment('课程范围 1小学2初中3高中4大学5其他6学前教育');
            $table->json('course_system')->nullable()->comment('课程体系');
            $table->json('grade')->nullable()->comment('年级范围');
            $table->string('region_code')->comment('归属地区编码');
            $table->string('description')->nullable()->comment('描述');
            $table->integer('created_by')->comment('创建人');
            $table->integer('updated_by')->comment('更新人');
            $table->softDeletesDatetime();
            $table->timestamps();
            $table->comment('学校');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('grade');
        Schema::dropIfExists('course_system');
        Schema::dropIfExists('region');
        Schema::dropIfExists('school');
    }
};
