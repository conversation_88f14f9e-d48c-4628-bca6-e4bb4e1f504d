<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('learning_plan', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('customer_id')->comment('客户ID')->index();
            $table->unsignedInteger('created_by')->comment('创建人')->index();
            $table->unsignedInteger('contact_id')->comment('联系人')->index();
            $table->string('title')->comment('标题');
            $table->string('school')->comment('学校 快照');
            $table->string('grade')->comment('年级 快照');
            $table->string('international_type')->comment('课程体系 快照');
            $table->text('target')->comment('目标');
            $table->string('mobile')->comment('联系电话');
            $table->date('consultant_date')->nullable()->comment('联系日期');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('learning_plan');
    }
};
