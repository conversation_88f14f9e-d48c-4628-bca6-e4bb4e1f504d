<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('score_subject', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('template_id')->comment('成绩规格ID');
            $table->string('subject_name')->comment('科目名称');
            $table->string('subject_score')->comment('科目总分');
            $table->unsignedInteger('sort')->comment('科目排序');
            $table->unsignedTinyInteger('is_count')->comment('是否计入总分');
            $table->unsignedTinyInteger('is_list')->comment('是否列表显示');
            $table->unsignedTinyInteger('is_must')->comment('是否必填');
            $table->unsignedInteger('max')->nullable()->comment('分数最大值');
            $table->unsignedInteger('min')->nullable()->comment('分数最小值');
            $table->json('pick_list')->nullable()->comment('对应成绩分项的下拉列表内容');
            $table->unsignedInteger('post_list_sort')->comment('0不显示 其余从小到大排序 不可重叠');
            $table->date('recommend_date')->nullable()->comment('推荐考试日期');
            $table->softDeletes();
            $table->timestamps();

            $table->index('template_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('score_subject');
    }
};
