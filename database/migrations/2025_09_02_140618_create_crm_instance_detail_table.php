<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('instance_detail', function (Blueprint $table) {
            $table->comment('产品实例&定价因素关系');
            $table->id('id');
            $table->integer('instance_id')->nullable()->comment('班级id');
            $table->integer('pricing_factor_id')->nullable()->comment('定价因素id');
            $table->integer('value_id')->nullable()->comment('定价因素对应value_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('instance_detail');
    }
};
