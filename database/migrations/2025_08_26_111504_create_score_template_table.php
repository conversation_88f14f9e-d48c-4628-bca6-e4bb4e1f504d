<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('score_template', function (Blueprint $table) {
            $table->id();
            $table->string('template_name')->comment('规格名称');
            $table->string('description')->comment('规格描述');
            $table->string('template_score')->comment('总分');
            $table->unsignedInteger('sort')->comment('排序');
//            $table->unsignedTinyInteger('status')->comment('状态');
            $table->unsignedTinyInteger('calculate_type')->comment('计算方式:0累加1取前端传值2取分项成绩）');
            $table->unsignedTinyInteger('is_hit')->comment('是否参加hit规则');
            $table->unsignedTinyInteger('is_repeat')->comment('能否一天重复考试');
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('score_template');
    }
};
