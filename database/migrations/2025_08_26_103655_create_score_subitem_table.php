<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('score_subitem', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('score_id')->comment('成绩ID');
            $table->unsignedInteger('subject_id')->comment('成绩科目ID');
            $table->string('score')->comment('分项成绩');
            $table->string('target')->comment('目标成绩');

            $table->index('score_id');
            $table->index('subject_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('score_subitem');
    }
};
