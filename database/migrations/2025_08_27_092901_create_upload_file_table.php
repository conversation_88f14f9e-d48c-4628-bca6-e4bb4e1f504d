<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('upload_file', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('creator_by')->comment('上传者ID')->index();
            $table->unsignedInteger('resource_fk')->default(0)->comment('资源外键')->index();
            $table->unsignedTinyInteger('resource_type')->default(0)->comment('资源类型:1成绩');
            $table->string('file_name')->comment('文件名称');
            $table->string('file_format')->comment('文件格式');
            $table->string('file_path')->comment('文件路径');
            $table->timestamp('created_at')->nullable();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('upload_file');
    }
};
