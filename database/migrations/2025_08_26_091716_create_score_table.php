<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('score', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('customer_id');
            $table->unsignedInteger('template_id')->comment('规格ID');
            $table->unsignedInteger('created_by')->comment('创建人');
            $table->unsignedTinyInteger('exam_type')->nullable()->comment('考试类型:1真考2模考3其他');
            $table->unsignedTinyInteger('score_status')->nullable()->comment('出分状态:1正常出分2未参加考试3取消成绩4延迟出分5不愿告知');
            $table->unsignedTinyInteger('have_writing')->nullable()->comment('是否考写作');
            $table->unsignedTinyInteger('is_xkt')->nullable()->comment('是否新课堂出分');
            $table->string('target')->comment('目标成绩');
            $table->string('score')->comment('实际成绩');
            $table->text('memo')->comment('备注');
            $table->date('exam_date')->nullable()->comment('考试时间');
            $table->softDeletes();
            $table->timestamps();

            $table->index('customer_id');
            $table->index('template_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('score');
    }
};
