<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('instance', function (Blueprint $table) {
            $table->comment('产品实例');
            $table->id('id');
            $table->integer('specification_id')->nullable()->comment('规格id');
            $table->string('code', 10)->nullable()->comment('编号');
            $table->string('name', 50)->nullable()->comment('产品名称');
            $table->dateTime('start_date')->nullable()->comment('产品开始时间');
            $table->dateTime('end_date')->nullable()->comment('产品结束时间');
            $table->tinyInteger('type')->nullable()->comment('1课程类 2计划类 3服务类 4其他 5咨询类');
            $table->tinyInteger('status')->nullable()->comment('0无效 1排课中 2排课完成/有效');
            $table->decimal('total', 8, 2)->nullable()->comment('产品课节总量');
            $table->tinyInteger('currency')->nullable()->comment('1美元 2人民币');
            $table->integer('min')->nullable()->comment('最小开班人数');
            $table->integer('max')->nullable()->comment('最大开班人数');
            $table->decimal('single_price', 20, 2)->nullable()->comment('单价');
            $table->decimal('total_price', 20, 2)->nullable()->comment('总价');
            $table->integer('custom_type')->nullable()->comment('1常规 2临时');
            $table->text('description')->nullable()->comment('描述');
            $table->text('service_time')->nullable()->comment('多人班授课时间');
            $table->tinyInteger('cas_status')->nullable()->comment('CAS排课状态 0未完成 1完成 -1流程中');
            $table->decimal('max_daily', 8, 2)->nullable()->comment('每日最大课量');
            $table->integer('reminder_level')->default(0)->comment('缴费提醒级别');
            $table->json('reminded')->nullable()->comment('已经提醒过的教师-开班信息提醒（json数组）');
            $table->dateTime('ready_time')->nullable()->comment('排课结束时间');
            $table->dateTime('order_start_time')->nullable()->comment('缴费窗口开始时间');
            $table->dateTime('order_end_time')->nullable()->comment('缴费窗口结束时间');
            $table->integer('campus_id')->nullable()->comment('校区id');
            $table->integer('level_id')->nullable()->comment('业务分级id');
            $table->integer('price_id')->nullable()->comment('对应价格参照id');
            $table->integer('price_type')->nullable()->comment('价格计算逻辑 0单价模式 1总价模式');
            $table->integer('created_by')->nullable()->comment('创建人');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('instance');
    }
};
