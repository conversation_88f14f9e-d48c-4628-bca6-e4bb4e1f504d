<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu', function (Blueprint $table) {
            $table->id();
            $table->string('name_cn')->comment('菜单中文名称');
            $table->string('name_en')->comment('菜单英文名称');
            $table->string('url')->comment('菜单URL');
            $table->string('icon')->comment('菜单图标');
            $table->integer('parent_id')->default(0)->comment('父菜单ID');
            $table->integer('sort')->default(0)->comment('排序');
            $table->integer('type')->default(0)->comment('类型 1:目录 2:菜单 3:按钮 4:动作');
            $table->integer('is_deleted')->default(0)->comment('是否删除');
            $table->integer('permission_id')->default(0)->comment('权限ID');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu');
    }
};
