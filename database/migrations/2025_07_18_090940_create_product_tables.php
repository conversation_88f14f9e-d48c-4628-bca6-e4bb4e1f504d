<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 产品线
        Schema::create('bu', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('产品线名称');
            $table->integer('is_deleted')->default(0);
            $table->comment('产品线');
        });

        // 价值链
        Schema::create('value_chain', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('价值链名称');
            $table->integer('is_deleted')->default(0);
            $table->comment('价值链');
        });

        // 价值链详情
        Schema::create('value_chain_detail', function (Blueprint $table) {
            $table->id();
            $table->integer('value_chain_id');
            $table->decimal('sign')->comment('签约占比');
            $table->decimal('teach')->comment('授课占比');
            $table->decimal('service')->comment('服务占比');
            $table->date('start_date')->comment('开始日期');
            $table->date('end_date')->comment('结束日期');
            $table->comment('价值链详情');
        });

        // 额外价值链
        Schema::create('additional_value_chain', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('额外价值链名称');
            $table->string('code')->comment('额外价值链编码,策略对应代码方法');
            $table->string('version')->comment('额外价值链版本');
            $table->decimal('sign')->comment('签约修正');
            $table->decimal('teach')->comment('授课修正');
            $table->decimal('service')->comment('服务修正');
            $table->integer('sort_order')->comment('优先度');
            $table->date('start_date')->comment('开始日期');
            $table->date('end_date')->comment('结束日期');
            $table->string('memo')->nullable()->comment('备注');
            $table->json('ext')->nullable()->comment('扩展字段');
            $table->comment('额外价值链');
        });

        // 产品类别
        Schema::create('product_category', function (Blueprint $table) {
            $table->id();
            $table->string('name_cn')->comment('中文名称');
            $table->string('name_en')->nullable()->comment('英文名称');
            $table->integer('type')->comment('类型 1课程类2计划类3服务类4其他5咨询类');
            $table->string('memo')->nullable()->comment('备注');
            $table->integer('status')->default(1)->comment('状态');
            $table->integer('value_chain_id')->comment('价值链');
            $table->integer('bu_id')->comment('产品线');
            $table->integer('created_by')->comment('创建人');
            $table->integer('updated_by')->default(0)->comment('更新人');
            $table->timestamps();
            $table->comment('产品类别');
        });

        // 产品业务分级
        Schema::create('product_level', function (Blueprint $table) {
            $table->id();
            $table->integer('category_id')->comment('产品类别');
            $table->string('name_cn')->comment('中文名称');
            $table->string('name_en')->nullable()->comment('英文名称');
            $table->integer('status')->default(1)->comment('状态');
            $table->comment('产品业务分级');
        });


        // 定价因素值
        Schema::create('pricing_factor_value', function (Blueprint $table) {
            $table->id();
            $table->integer('pricing_factor_id')->comment('定价因素');
            $table->string('name_cn')->comment('中文名称');
            $table->string('name_en')->nullable()->comment('英文名称');
            $table->integer('status')->default(1)->comment('状态');
            $table->comment('定价因素值');
        });

        // 产品类别定价因素
        Schema::create('product_category_factor', function (Blueprint $table) {
            $table->id();
            $table->integer('category_id')->comment('产品类别');
            $table->integer('pricing_factor_id')->comment('定价因素');
            $table->comment('产品类别定价因素');
        });

        // 科目
        Schema::create('subject', function (Blueprint $table) {
            $table->id();
            $table->string('name_cn')->comment('中文名称');
            $table->string('name_en')->nullable()->comment('英文名称');
            $table->integer('status')->default(1)->comment('状态');
            $table->comment('科目');
        });

        // 绩效类型
        Schema::create('achievement', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('中文名称');
            $table->integer('status')->default(1)->comment('状态');
            $table->comment('绩效类型');
        });

        // 产品规格
        Schema::create('product_specification', function (Blueprint $table) {
            $table->id();
            $table->integer('category_id')->comment('产品类别');
            $table->string('code')->comment('规格编码');
            $table->string('name_cn')->comment('中文名称');
            $table->string('name_en')->nullable()->comment('英文名称');
            $table->string('memo')->nullable()->comment('备注');
            $table->integer('exceeding')->default(0)->comment('计划类产品是否允许超额 0否 1是');
            $table->integer('currency')->default(2)->comment('币种');
            $table->integer('status')->default(1)->comment('状态');
            $table->integer('level_id')->nullable()->comment('业务分级');
            $table->integer('achievement_id')->comment('绩效类型');
            $table->integer('custom_price')->default(0)->comment('是否支持自定义价格 0否 1是');
            $table->integer('price_type')->default(0)->comment('价格类型 0单价 1总价');
            $table->integer('static_quantity')->default(0)->comment('总价模式下的固定数量');
            $table->integer('region')->nullable()->comment('地区');
            $table->integer('class_type')->nullable()->comment('班型');
            $table->integer('teacher_type')->nullable()->comment('教师类型');
            $table->integer('pricing_type')->nullable()->comment('定价方案');
            $table->integer('plan_tag_id')->nullable()->comment('计划产品适用tag');
            $table->integer('created_by')->comment('创建人');
            $table->integer('updated_by')->comment('更新人');
            $table->timestamps();
            $table->comment('产品规格');
        });

        // 产品规格科目
        Schema::create('product_specification_subjects', function (Blueprint $table) {
            $table->id();
            $table->integer('specification_id')->comment('产品规格');
            $table->integer('subject_id')->comment('科目');
            $table->integer('quantity')->comment('数量');
            $table->integer('nct_quantity')->comment('nct数量,大于0代表该科目支持nct');
            $table->comment('产品规格科目');
        });


        // 产品规格价格
        Schema::create('product_specification_price', function (Blueprint $table) {
            $table->id();
            $table->integer('specification_id')->comment('产品规格');
            $table->decimal('price', 14, 2)->comment('价格');
            $table->date('start_date')->comment('开始日期');
            $table->date('end_date')->comment('结束日期');
            $table->integer('status')->default(1)->comment('状态');
            $table->integer('created_by')->comment('创建人');
            $table->datetime('created_at')->comment('创建时间');
            $table->comment('产品规格价格');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bu');
        Schema::dropIfExists('value_chain');
        Schema::dropIfExists('value_chain_detail');
        Schema::dropIfExists('additional_value_chain');
        Schema::dropIfExists('product_category');
        Schema::dropIfExists('product_level');
        Schema::dropIfExists('pricing_factor_value');
        Schema::dropIfExists('product_category_factor');
        Schema::dropIfExists('subject');
        Schema::dropIfExists('achievement');
        Schema::dropIfExists('product_specification');
        Schema::dropIfExists('product_specification_subjects');
        Schema::dropIfExists('product_specification_price');
    }
};
