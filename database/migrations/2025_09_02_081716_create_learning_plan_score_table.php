<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('learning_plan_score', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('learning_plan_id')->comment('学习规划ID')->index();
            $table->unsignedInteger('score_id')->comment('成绩ID')->index();
            $table->unsignedTinyInteger('type')->comment('成绩类型:0托福,1PreA,2ABC,3其他');
            $table->date('exam_date')->nullable()->comment('考试时间');
            $table->string('score_detail')->comment('分数详情');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('learning_plan_score');
    }
};
