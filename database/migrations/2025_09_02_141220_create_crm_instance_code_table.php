<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('instance_code', function (Blueprint $table) {
            $table->comment('产品编号生成');
            $table->id();
            $table->string('type', 1)->comment('课程类C');
            $table->string('date', 6)->comment('日期段：083025');
            $table->string('letter', 1)->comment('字母段A、B');
            $table->string('sequence', 2)->comment('序列段01、02、03。。。');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('instance_code');
    }
};
